import { Link } from "react-router-dom";

const DropdownItem = ({
  tag = "button",
  to,
  className,
  children,
  onItemClick,
  ...props
}) => {
  if (tag === "a") {
    return (
      <Link to={to} className={className} onClick={onItemClick} {...props}>
        {children}
      </Link>
    );
  }

  return (
    <button className={className} onClick={onItemClick} {...props}>
      {children}
    </button>
  );
};

export default DropdownItem;
