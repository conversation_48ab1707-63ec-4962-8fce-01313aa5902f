import React from "react";

const GridShape = () => {
  return (
    <div className="absolute -z-10 top-0 left-0 right-0 bottom-0 bg-[#F4F7FF] dark:bg-slate-900 lg:bg-transparent lg:dark:bg-transparent">
      <div className="absolute left-0 top-0 -z-10 h-full w-full">
        <div className="absolute left-0 top-0 -z-10 h-full w-full">
          <div className="absolute left-0 top-0 z-0 opacity-30 dark:opacity-5">
            <div className="absolute left-0 top-0 h-full w-full bg-gradient-to-b from-transparent to-white dark:to-black"></div>
            <div className="grid h-full w-full grid-cols-12 gap-4 gap-y-0">
              {Array(12)
                .fill()
                .map((_, index) => (
                  <div
                    key={index}
                    className="border-r border-black/5 dark:border-white/5"
                  ></div>
                ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GridShape;
