import Chart from "react-apexcharts";
import { useState, useEffect } from "react";

import api from "../../axios/axiosInstance";

export default function StatisticsChart() {
  const [salesData, setSalesData] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);

  // Set default date range - one month range ending today
  const getDefaultEndDate = () => {
    return new Date();
  };

  const getDefaultStartDate = () => {
    const date = new Date();
    date.setDate(date.getDate() - 15); // One month back
    return date;
  };

  const [startDate, setStartDate] = useState(getDefaultStartDate());
  const [endDate, setEndDate] = useState(getDefaultEndDate());

  // Format date as YYYY-MM-DD for API request
  const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  useEffect(() => {
    const fetchSalesData = async () => {
      try {
        setLoading(true);

        // Use hard-coded dates for testing if you're still having issues
        // const response = await api.get("/api/admin/sales-revenue-by-date/?start_date=2025-05-01&end_date=2025-05-23");

        const response = await api.get("/api/admin/sales-revenue-by-date/", {
          params: {
            start_date: formatDate(startDate),
            end_date: formatDate(endDate),
          },
        });


        if (response.data && response.data.status) {
          const revenueData = response.data.data.sales_revenue;

          if (revenueData && Object.keys(revenueData).length > 0) {
            // Extract dates and values
            const dates = Object.keys(revenueData);
            const values = Object.values(revenueData);

            // Format dates to show as DD/MM format
            const formattedDates = dates.map((date) => {
              const [year, month, day] = date.split("-");
              return `${day}/${month}`;
            });

            setCategories(formattedDates);
            setSalesData(values);
            
          } else {
            
            setCategories(["No Data"]);
            setSalesData([0]);
          }
        } else {
          console.log(
            "API response status is not true or response format is incorrect"
          );
          setCategories(["No Data"]);
          setSalesData([0]);
        }
      } catch (error) {
        console.error("Error fetching sales data:", error);
        setCategories(["Error"]);
        setSalesData([0]);
      } finally {
        setLoading(false);
      }
    };

    fetchSalesData();
  }, [startDate, endDate]);

  const options = {
    legend: {
      show: false,
      position: "top",
      horizontalAlign: "left",
    },
    colors: ["#465FFF"],
    chart: {
      fontFamily: "Outfit, sans-serif",
      height: 310,
      type: "line",
      toolbar: {
        show: false,
      },
      zoom: {
        enabled: false,
      },
    },
    stroke: {
      curve: "smooth",
      width: 3,
    },
    fill: {
      type: "gradient",
      gradient: {
        opacityFrom: 0.55,
        opacityTo: 0,
      },
    },
    markers: {
      size: 4,
      strokeColors: "#fff",
      strokeWidth: 2,
      hover: {
        size: 6,
      },
    },
    grid: {
      xaxis: {
        lines: {
          show: false,
        },
      },
      yaxis: {
        lines: {
          show: true,
        },
      },
      padding: {
        top: 0,
        right: 0,
        bottom: 0,
        left: 10,
      },
    },
    dataLabels: {
      enabled: false,
    },
    tooltip: {
      enabled: true,
      custom: function ({ series, seriesIndex, dataPointIndex, w }) {
        const date = w.globals.labels[dataPointIndex];
        const value = series[seriesIndex][dataPointIndex];
        return `
          <div class="p-2 bg-white dark:bg-gray-800 shadow-md rounded-md">
            <div class="text-xs text-gray-500 dark:text-gray-400">Date: ${date}</div>
            <div class="text-sm font-medium">₹${value.toFixed(2)}</div>
          </div>
        `;
      },
    },
    xaxis: {
      type: "category",
      categories: categories.length > 0 ? categories : ["No Data"],
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
      labels: {
        rotate: -45,
        style: {
          fontSize: "11px",
          fontFamily: "Outfit, sans-serif",
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          fontSize: "12px",
          colors: ["#6B7280"],
        },
        formatter: (value) => `₹${value.toFixed(0)}`,
      },
      title: {
        text: "",
        style: {
          fontSize: "0px",
        },
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 280,
          },
          xaxis: {
            labels: {
              rotate: -90,
              offsetY: 5,
            },
          },
        },
      },
    ],
  };

  const series = [
    {
      name: "Daily Revenue",
      data: salesData.length > 0 ? salesData : [0],
    },
  ];

  return (
    <div className="rounded-2xl border border-gray-200 bg-white px-5 pb-5 pt-5 dark:border-gray-800 dark:bg-white/[0.03] sm:px-6 sm:pt-6">
      <div className="flex flex-col gap-5 mb-6 sm:flex-row sm:justify-between">
        <div className="w-full sm:w-auto">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90">
            Daily Sales Revenue
          </h3>
          <p className="mt-1 text-gray-500 text-theme-sm dark:text-gray-400">
            Revenue statistics for each day (DD/MM)
          </p>
        </div>
      </div>

      <div className="w-full overflow-hidden">
        {loading ? (
          <div className="flex justify-center items-center h-[310px]">
            <p className="text-gray-500">Loading chart data...</p>
          </div>
        ) : salesData.length === 1 && salesData[0] === 0 ? (
          <div className="flex justify-center items-center h-[310px]">
            <p className="text-gray-500">
              No data available for the selected date range
            </p>
          </div>
        ) : (
          <Chart
            options={options}
            series={series}
            type="area"
            height={310}
            width="100%"
          />
        )}
      </div>
    </div>
  );
}
