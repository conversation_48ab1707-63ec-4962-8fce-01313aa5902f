import React, { useState, useEffect } from "react";
import api from "../../axios/axiosInstance";
import URL from "../../axios/URl";

function TopSellingProduct() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchTopSellingProducts = async () => {
      try {
        const response = await api.get("/api/top-selling-products");
        if (response.data.status) {
          // Sort by sales_count in descending order and take top 5
          const sortedProducts = response.data.data
            .sort((a, b) => b.sales_count - a.sales_count)
            .slice(0, 5);
          setProducts(sortedProducts);
        } else {
          setError("Failed to fetch products");
        }
      } catch (err) {
        setError("Error fetching products: " + err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchTopSellingProducts();
  }, []);

  if (error) return <div>Error: {error}</div>;

  return (
    <div className="overflow-hidden rounded-2xl border border-gray-200 bg-white px-4 pb-3 pt-4 dark:border-gray-800 dark:bg-white/[0.03] sm:px-6">
      <div className="flex flex-col gap-2 mb-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90">
            Top Selling Products
          </h3>
        </div>
      </div>

      <div className="max-w-full overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-100 dark:divide-gray-800">
          <thead className="border-y border-gray-100 dark:border-gray-800">
            <tr>
              <th className="py-3 px-4 text-left font-medium text-gray-500 text-theme-xs dark:text-gray-400">
                Product
              </th>
              <th className="py-3 px-4 text-left font-medium text-gray-500 text-theme-xs dark:text-gray-400">
                Category
              </th>
              <th className="py-3 px-4 text-left font-medium text-gray-500 text-theme-xs dark:text-gray-400">
                Price
              </th>
              <th className="py-3 px-4 text-left font-medium text-gray-500 text-theme-xs dark:text-gray-400">
                Discount
              </th>
              <th className="py-3 px-4 text-left font-medium text-gray-500 text-theme-xs dark:text-gray-400">
                Rating
              </th>
              <th className="py-3 px-4 text-left font-medium text-gray-500 text-theme-xs dark:text-gray-400">
                Sales
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100 dark:divide-gray-800">
            {loading ? (
              <tr>
                <td colSpan="6" className="py-4 px-4 text-center text-gray-500">
                  Loading...
                </td>
              </tr>
            ) : products.length > 0 ? (
              products.map((product) => (
                <tr
                  key={product.id}
                  className="hover:bg-gray-50 dark:hover:bg-white/[0.02]"
                >
                  <td className="py-3 px-4">
                    <div className="flex items-center gap-3">
                      <div className="h-[40px] w-[40px] overflow-hidden rounded-md">
                        <img
                          src={`${URL.PHOTO_URL}${product.image[0]}`}
                          alt={product.product_name}
                          className="h-full w-full object-cover"
                          onError={(e) => {
                            e.target.src =
                              "https://via.placeholder.com/40?text=Product";
                          }}
                        />
                      </div>
                      <div>
                        <p className="font-medium text-gray-800 text-theme-sm dark:text-white/90">
                          {product.product_name}
                        </p>
                      </div>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-gray-600 text-theme-sm dark:text-gray-400">
                    {product.category_name}
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex flex-col">
                      <span className="font-medium text-gray-800 text-theme-sm dark:text-white/90">
                        ₹{product.product_price.toFixed(2)}
                      </span>
                      {product.product_old_price > 0 && (
                        <span className="text-gray-500 text-theme-xs line-through">
                          ₹{product.product_old_price.toFixed(2)}
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="2xsm:py-2 2xsm:px-2 sm:py-3 sm:px-4">
                    {product.discount_percentage > 0 ? (
                      <span className="2xsm:px-1.5 2xsm:py-1 sm:px-2 sm:py-1 rounded-full text-theme-xs font-medium bg-green-100 text-green-700">
                        {product.discount_percentage}% OFF
                      </span>
                    ) : (
                      <span className="text-gray-500">-</span>
                    )}
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex items-center">
                      <span className="text-yellow-500 mr-1">★</span>
                      <span className="text-black dark:text-white">
                        {product.average_rating.toFixed(1)}
                      </span>
                      <span className="text-gray-500 text-xs ml-1">
                        ({product.total_reviews})
                      </span>
                    </div>
                  </td>
                  <td className="py-3 px-4 font-medium text-gray-800 dark:text-white/90">
                    {product.sales_count}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="6" className="py-4 px-4 text-center text-gray-500">
                  No products found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default TopSellingProduct;
