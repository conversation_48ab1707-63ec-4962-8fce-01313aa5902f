import { useEffect, useState } from "react";
import api from "../../axios/axiosInstance";
import URL from "../../axios/URl";

// Simple Badge component for order status
const Badge = ({ children, color }) => {
  const colorClasses = {
    success: "bg-green-100 text-green-700",
    warning: "bg-yellow-100 text-yellow-700",
    error: "bg-red-100 text-red-700",
    primary: "bg-blue-100 text-blue-700",
  };

  return (
    <span
      className={`px-2 py-1 rounded-full text-xs font-medium ${
        colorClasses[color] || colorClasses.primary
      }`}
    >
      {children}
    </span>
  );
};

export default function RecentOrders() {
  const [recentOrders, setRecentOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [expandedOrder, setExpandedOrder] = useState(null);

  useEffect(() => {
    const fetchRecentOrders = async () => {
      try {
        setLoading(true);
        const response = await api.get("/api/recent-orders");
        setRecentOrders(response.data.recent_orders || []);
      } catch (error) {
        console.log(error);
      } finally {
        setLoading(false);
      }
    };
    fetchRecentOrders();
  }, []);

  // Function to format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Default image URLs
  const defaultUserImage = "https://via.placeholder.com/40?text=User";
  const defaultProductImage = "https://via.placeholder.com/30?text=Product";

  // Toggle expanded view for an order
  const toggleExpandOrder = (orderId) => {
    if (expandedOrder === orderId) {
      setExpandedOrder(null);
    } else {
      setExpandedOrder(orderId);
    }
  };

  return (
    <div className="overflow-hidden rounded-2xl border border-gray-200 bg-white px-4 pb-3 pt-4 dark:border-gray-800 dark:bg-white/[0.03] sm:px-6">
      <div className="flex flex-col gap-2 mb-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90">
            Recent Orders
          </h3>
        </div>

        
      </div>
      <div className="max-w-full overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-100 dark:divide-gray-800">
          <thead className="border-y border-gray-100 dark:border-gray-800">
            <tr>
              <th className="py-3 px-4 text-left font-medium text-gray-500 text-theme-xs dark:text-gray-400">
                Order ID
              </th>
              <th className="py-3 px-4 text-left font-medium text-gray-500 text-theme-xs dark:text-gray-400">
                Customer
              </th>
              <th className="py-3 px-4 text-left font-medium text-gray-500 text-theme-xs dark:text-gray-400">
                Products
              </th>
              <th className="py-3 px-4 text-left font-medium text-gray-500 text-theme-xs dark:text-gray-400">
                Amount
              </th>
              <th className="py-3 px-4 text-left font-medium text-gray-500 text-theme-xs dark:text-gray-400">
                Date
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100 dark:divide-gray-800">
            {loading ? (
              <tr>
                <td colSpan="5" className="py-4 px-4 text-center text-gray-500">
                  Loading...
                </td>
              </tr>
            ) : Array.isArray(recentOrders) && recentOrders.length > 0 ? (
              recentOrders.map((order) => (
                
                  <tr
                    key={order.order_id}
                    className="hover:bg-gray-50 dark:hover:bg-white/[0.02]"
                  >
                    <td className="py-3 px-4">
                      <Badge color="primary">#{order.order_id}</Badge>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-3">
                        <div className="h-[40px] w-[40px] overflow-hidden rounded-full">
                          <img
                            src={
                              order.profile_image
                                ? `${URL.PHOTO_URL}${order.profile_image}`
                                : defaultUserImage
                            }
                            className="h-[40px] w-[40px] object-cover"
                            alt={order.name || "User"}
                            onError={(e) => {
                              e.target.src = defaultUserImage;
                            }}
                          />
                        </div>
                        <div>
                          <p className="font-medium text-gray-800 text-theme-sm dark:text-white/90">
                            {order.name || "Unknown User"}
                          </p>
                          <span className="text-gray-500 text-theme-xs dark:text-gray-400">
                            {order.email || "No email"}
                          </span>
                        </div>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex flex-col gap-1">
                        {(expandedOrder === order.order_id
                          ? order.products
                          : order.products.slice(0, 2)
                        ).map((product) => (
                          <div
                            key={product.product_id}
                            className="flex items-center gap-2"
                          >
                            <div className="h-[30px] w-[30px] overflow-hidden rounded-md">
                              <img
                                src={
                                  product.image
                                    ? `${URL.PHOTO_URL}${product.image}`
                                    : defaultProductImage
                                }
                                className="h-[30px] w-[30px] object-cover"
                                alt={product.title || "Product"}
                                onError={(e) => {
                                  e.target.src = defaultProductImage;
                                }}
                              />
                            </div>
                            <span className="text-gray-600 text-theme-xs dark:text-gray-400">
                              {product.title || "Unnamed Product"} x
                              {product.quantity}
                            </span>
                          </div>
                        ))}
                        {order.products.length > 2 &&
                          expandedOrder !== order.order_id && (
                            <button
                              onClick={() => toggleExpandOrder(order.order_id)}
                              className="text-blue-600 text-theme-xs hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mt-1"
                            >
                              + View {order.products.length - 2} more items
                            </button>
                          )}
                        {expandedOrder === order.order_id && (
                          <button
                            onClick={() => toggleExpandOrder(order.order_id)}
                            className="text-blue-600 text-theme-xs hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mt-1"
                          >
                            Show less
                          </button>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4 font-medium text-gray-800 text-theme-sm dark:text-white/90">
                      ₹{order.total_amount.toFixed(2)}
                    </td>
                    <td className="2xsm:py-2 2xsm:px-0.5 sm:py-3 sm:px-4  text-gray-500 text-theme-sm dark:text-gray-400">
                      {formatDate(order.created_at)}
                    </td>
                  </tr>
                
              ))
            ) : (
              <tr>
                <td colSpan="5" className="py-4 px-4 text-center text-gray-500">
                  No orders found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
