import {
  ArrowDownIcon,
  ArrowUpIcon,
  BoxIconLine,
  DollarLineIcon,
  GroupIcon,
} from "../../icons";

import api from "../../axios/axiosInstance";
import { useEffect, useState } from "react";
import { IndianRupee } from "lucide-react";

export default function EcommerceMetrics() {
  const [metrics, SetMetrics] = useState([]);

  // Calculate percentage based on value
  const calculatePercentage = (value, type) => {
    if (!value) return 0;

    switch (type) {
      case "products":
        // For products: 100 products = 100%
        return Math.min(100, (value / 1000) * 100);
      case "revenue":
        // For revenue: 100000 = 100%
        return Math.min(100, (value / 100000) * 100);
      case "customers":
        // For customers: 100 customers = 100%
        return Math.min(100, (value / 1000) * 100);
      default:
        return 0;
    }
  };

  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        const response = await api.get("/api/admin-dashboard");
        SetMetrics(response);
      } catch (error) {
        console.log(error);
      }
    };
    fetchMetrics();
  }, []);

  const productsPercentage = calculatePercentage(
    metrics?.data?.dashboard?.total_products,
    "products"
  );
  const revenuePercentage = calculatePercentage(
    metrics?.data?.dashboard?.total_revenue,
    "revenue"
  );
  const customersPercentage = calculatePercentage(
    metrics?.data?.dashboard?.total_users,
    "customers"
  );

  return (
    <div className="grid md:grid-cols-3 gap-4 sm:grid-cols-2 md:gap-6">
      {/* <!-- Metric Item Start --> */}
      <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] md:p-6">
        <div className="flex items-center justify-center w-12 h-12 bg-gray-100 rounded-xl dark:bg-gray-800">
          <BoxIconLine className="text-gray-800 size-6 dark:text-white/90" />
        </div>

        <div className="mt-5">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            Products
          </span>
          <div className="flex items-end justify-between mt-2">
            <h4 className="font-bold text-gray-800 text-3xl dark:text-white/90">
              {metrics?.data?.dashboard?.total_products || "0"}
            </h4>
            <div className="flex items-center text-green-500">
              <ArrowUpIcon className="size-4 mr-1" />
              <span className="font-medium">
                {productsPercentage.toFixed(2)}%
              </span>
            </div>
          </div>
        </div>
      </div>
      {/* <!-- Metric Item End --> */}

      {/* <!-- Metric Item Start --> */}
      <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] md:p-6">
        <div className="flex items-center justify-center w-12 h-12 bg-gray-100 rounded-xl dark:bg-gray-800">
          <IndianRupee className="text-gray-800 size-6 dark:text-white/90" />
        </div>
        <div className="mt-5">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            Revenue
          </span>
          <div className="flex items-end justify-between mt-2">
            <h4 className="font-bold text-gray-800 text-3xl dark:text-white/90">
              {metrics?.data?.dashboard?.total_revenue || "0"}
            </h4>
            <div className="flex items-center text-green-500">
              <ArrowUpIcon className="size-4 mr-1" />
              <span className="font-medium">
                {revenuePercentage.toFixed(2)}%
              </span>
            </div>
          </div>
        </div>
      </div>
      {/* <!-- Metric Item End --> */}

      {/* <!-- Metric Item Start --> */}
      <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] md:p-6">
        <div className="flex items-center justify-center w-12 h-12 bg-gray-100 rounded-xl dark:bg-gray-800">
          <GroupIcon className="text-gray-800 size-6 dark:text-white/90" />
        </div>
        <div className="mt-5">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            Customers
          </span>
          <div className="flex items-end justify-between mt-2">
            <h4 className="font-bold text-gray-800 text-3xl dark:text-white/90">
              {metrics?.data?.dashboard?.total_users || "0"}
            </h4>
            <div className="flex items-center text-green-500">
              <ArrowUpIcon className="size-4 mr-1" />
              <span className="font-medium">
                {customersPercentage.toFixed(2)}%
              </span>
            </div>
          </div>
        </div>
      </div>
      {/* <!-- Metric Item End --> */}
    </div>
  );
}
