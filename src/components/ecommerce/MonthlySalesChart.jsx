import Chart from "react-apexcharts";
import { MoreDotIcon } from "../../icons";
import { useState, useEffect } from "react";
import Dropdown from "../common/Dropdown";
import DropdownItem from "../common/DropdownItem";
import api from "../../axios/axiosInstance";

export default function MonthlySalesChart() {
  const [orderData, setOrderData] = useState([]);
  const [categories, setCategories] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [startDate, setStartDate] = useState(getDefaultStartDate());
  const [endDate, setEndDate] = useState(new Date());

  // Get default start date (30 days ago)
  function getDefaultStartDate() {
    const date = new Date();
    date.setDate(date.getDate() - 15);
    return date;
  }

  // Format date for API
  function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  }

  // Format date for display (DD/MM)
  function formatDateForDisplay(dateString) {
    const date = new Date(dateString);
    return `${date.getDate()}/${date.getMonth() + 1}`;
  }

  useEffect(() => {
    fetchOrderCounts();
  }, [startDate, endDate]);

  const fetchOrderCounts = async () => {
    try {
      setIsLoading(true);
      const response = await api.get("/api/admin/order-counts-by-date/", {
        params: {
          start_date: formatDate(startDate),
          end_date: formatDate(endDate),
        },
      });

      const result = response.data;

      if (result.status && result.data.order_counts) {
        const orderCounts = result.data.order_counts;
        const dates = Object.keys(orderCounts);
        const counts = Object.values(orderCounts);

        // Format dates for display
        const formattedDates = dates.map((date) => formatDateForDisplay(date));

        setCategories(formattedDates);
        setOrderData(counts);
      } else {
        setCategories([]);
        setOrderData([]);
      }
    } catch (error) {
      console.error("Error fetching order counts:", error);
      setCategories([]);
      setOrderData([]);
    } finally {
      setIsLoading(false);
    }
  };

  const options = {
    colors: ["#465fff"],
    chart: {
      fontFamily: "Outfit, sans-serif",
      type: "bar",
      height: 180,
      toolbar: {
        show: false,
      },
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "39%",
        borderRadius: 5,
        borderRadiusApplication: "end",
      },
    },
    dataLabels: {
      enabled: false,
    },
    stroke: {
      show: true,
      width: 4,
      colors: ["transparent"],
    },
    xaxis: {
      categories: categories.length > 0 ? categories : ["No Data"],
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
    },
    legend: {
      show: false,
      position: "top",
      horizontalAlign: "left",
      fontFamily: "Outfit",
    },
    yaxis: {
      title: {
        text: "",
      },
    },
    grid: {
      yaxis: {
        lines: {
          show: false,
        },
      },
    },
    fill: {
      opacity: 1,
    },
    tooltip: {
      x: {
        show: true,
      },
      y: {
        formatter: (val) => `${val} orders`,
      },
    },
  };

  const series = [
    {
      name: "Orders",
      data: orderData.length > 0 ? orderData : [0],
    },
  ];

  const [isOpen, setIsOpen] = useState(false);

  function toggleDropdown() {
    setIsOpen(!isOpen);
  }

  function closeDropdown() {
    setIsOpen(false);
  }

  function refreshData() {
    fetchOrderCounts();
    closeDropdown();
  }

  return (
    <div className="overflow-hidden rounded-2xl border border-gray-200 bg-white px-5 pt-5 dark:border-gray-800 dark:bg-white/[0.03] sm:px-6 sm:pt-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90">
          Daily Orders
        </h3>
       
      </div>

      <div className="max-w-full overflow-x-auto custom-scrollbar">
        {isLoading ? (
          <div className="flex justify-center items-center h-[180px]">
            <p className="text-gray-500 dark:text-gray-400">Loading data...</p>
          </div>
        ) : orderData.length === 0 ? (
          <div className="flex justify-center items-center h-[180px]">
            <p className="text-gray-500 dark:text-gray-400">
              No order data available
            </p>
          </div>
        ) : (
          <div className="-ml-5 min-w-[650px] xl:min-w-full pl-2">
            <Chart options={options} series={series} type="bar" height={180} />
          </div>
        )}
      </div>
    </div>
  );
}
