import { useState } from "react";
import Dropdown from "../common/Dropdown";
import DropdownItem from "../common/DropdownItem";

const NotificationDropdown = () => {
  const [isOpen, setIsOpen] = useState(false);

  function toggleDropdown() {
    setIsOpen(!isOpen);
  }

  function closeDropdown() {
    setIsOpen(false);
  }

  return (
    <div className="relative">
      <button
        onClick={toggleDropdown}
        className="flex items-center justify-center w-10 h-10 text-gray-700 rounded-lg hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800"
      >
        <svg
          className="fill-gray-500 dark:fill-gray-400"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M12 2C10.3431 2 9 3.34315 9 5V5.31335C6.00979 6.19492 4 8.87195 4 12V15.7324C4 16.1051 3.83858 16.4577 3.56282 16.7145L3.4 16.8619C2.72711 17.4926 3.16232 18.6825 4.08746 18.9084C6.94302 19.6091 9.45242 20 12 20C14.5476 20 17.057 19.6091 19.9125 18.9084C20.8377 18.6825 21.2729 17.4926 20.6 16.8619L20.4372 16.7145C20.1614 16.4577 20 16.1051 20 15.7324V12C20 8.87195 17.9902 6.19492 15 5.31335V5C15 3.34315 13.6569 2 12 2ZM13.5 5V5.12636C13.0119 5.04201 12.5116 5 12 5C11.4884 5 10.9881 5.04201 10.5 5.12636V5C10.5 4.17157 11.1716 3.5 12 3.5C12.8284 3.5 13.5 4.17157 13.5 5ZM5.5 12C5.5 9.51472 7.51472 7.5 10 7.5H14C16.4853 7.5 18.5 9.51472 18.5 12V15.7324C18.5 16.4904 18.8242 17.2181 19.3757 17.7317L19.5385 17.8791C19.5795 17.9176 19.5933 17.9393 19.5984 17.9507C19.6001 17.9544 19.6006 17.9568 19.6004 17.9579C19.6002 17.959 19.5995 17.9608 19.5972 17.9633C19.5935 17.9673 19.5855 17.9743 19.5672 17.9812C19.5276 17.9955 19.4486 18.0164 19.2967 18.0508C16.5454 18.7259 14.1474 19.0957 11.7403 19.0957C9.33308 19.0957 6.93515 18.7259 4.18332 18.0508C4.03142 18.0164 3.95243 17.9955 3.91278 17.9812C3.89454 17.9743 3.88648 17.9673 3.88282 17.9633C3.88048 17.9608 3.87979 17.959 3.87959 17.9579C3.87938 17.9568 3.87991 17.9544 3.88159 17.9507C3.88671 17.9393 3.90051 17.9176 3.94155 17.8791L4.10436 17.7317C4.65584 17.2181 4.98 16.4904 4.98 15.7324V12Z"
            fill=""
          />
          <path
            d="M9 20.75C9 20.3358 9.33579 20 9.75 20H14.25C14.6642 20 15 20.3358 15 20.75C15 21.1642 14.6642 21.5 14.25 21.5H9.75C9.33579 21.5 9 21.1642 9 20.75Z"
            fill=""
          />
        </svg>
      </button>

      <Dropdown
        isOpen={isOpen}
        onClose={closeDropdown}
        className="absolute right-0 mt-[17px] flex w-[400px] flex-col rounded-2xl border border-gray-200 bg-white p-5 shadow-theme-lg dark:border-gray-800 dark:bg-gray-dark"
      >
        <div className="flex items-center justify-between pb-5 border-b border-gray-200 dark:border-gray-800">
          <h5 className="font-semibold text-gray-700 text-theme-md dark:text-white">
            Notifications
          </h5>
          <span className="px-2 py-1 text-xs font-medium text-white rounded-full bg-brand-500">
            05
          </span>
        </div>

        <ul className="flex flex-col gap-5 py-5">
          <li>
            <DropdownItem onItemClick={closeDropdown} className="flex gap-3">
              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-brand-50 dark:bg-brand-900/20">
                <svg
                  className="fill-brand-500 dark:fill-brand-400"
                  width="18"
                  height="18"
                  viewBox="0 0 18 18"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M16.7695 9.25C16.7695 11.5288 15.8628 13.7146 14.2507 15.3267C12.6387 16.9387 10.4529 17.8455 8.1741 17.8455C5.89526 17.8455 3.70942 16.9387 2.09739 15.3267C0.485352 13.7146 -0.421387 11.5288 -0.421387 9.25C-0.421387 6.97116 0.485352 4.78532 2.09739 3.17329C3.70942 1.56125 5.89526 0.654511 8.1741 0.654511C10.4529 0.654511 12.6387 1.56125 14.2507 3.17329C15.8628 4.78532 16.7695 6.97116 16.7695 9.25ZM8.1741 4.58011C8.1741 4.48163 8.13494 4.38715 8.06468 4.31689C7.99442 4.24663 7.89994 4.20747 7.80146 4.20747C7.70298 4.20747 7.6085 4.24663 7.53824 4.31689C7.46798 4.38715 7.42882 4.48163 7.42882 4.58011V9.25C7.42882 9.34848 7.46798 9.44296 7.53824 9.51322C7.6085 9.58348 7.70298 9.62264 7.80146 9.62264H10.8348C10.9333 9.62264 11.0278 9.58348 11.098 9.51322C11.1683 9.44296 11.2075 9.34848 11.2075 9.25C11.2075 9.15152 11.1683 9.05704 11.098 8.98678C11.0278 8.91652 10.9333 8.87736 10.8348 8.87736H8.1741V4.58011Z"
                    fill=""
                  />
                </svg>
              </div>
              <div>
                <p className="font-medium text-gray-700 text-theme-sm dark:text-white">
                  Your order is placed
                </p>
                <p className="mt-1 text-gray-500 text-theme-xs dark:text-gray-400">
                  Amet minim mollit non deserunt ullamco est sit aliqua dolor do
                </p>
                <p className="mt-1 text-gray-400 text-theme-xs dark:text-gray-500">
                  3 min ago
                </p>
              </div>
            </DropdownItem>
          </li>
          <li>
            <DropdownItem onItemClick={closeDropdown} className="flex gap-3">
              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-green-50 dark:bg-green-900/20">
                <svg
                  className="fill-green-500 dark:fill-green-400"
                  width="18"
                  height="18"
                  viewBox="0 0 18 18"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M16.7695 9.25C16.7695 11.5288 15.8628 13.7146 14.2507 15.3267C12.6387 16.9387 10.4529 17.8455 8.1741 17.8455C5.89526 17.8455 3.70942 16.9387 2.09739 15.3267C0.485352 13.7146 -0.421387 11.5288 -0.421387 9.25C-0.421387 6.97116 0.485352 4.78532 2.09739 3.17329C3.70942 1.56125 5.89526 0.654511 8.1741 0.654511C10.4529 0.654511 12.6387 1.56125 14.2507 3.17329C15.8628 4.78532 16.7695 6.97116 16.7695 9.25ZM11.5348 7.69844C11.6051 7.62818 11.6443 7.5337 11.6443 7.43522C11.6443 7.33674 11.6051 7.24226 11.5348 7.172C11.4646 7.10174 11.3701 7.06258 11.2716 7.06258C11.1732 7.06258 11.0787 7.10174 11.0084 7.172L7.80146 10.379L6.2125 8.79C6.14224 8.71974 6.04776 8.68058 5.94928 8.68058C5.8508 8.68058 5.75632 8.71974 5.68606 8.79C5.6158 8.86026 5.57664 8.95474 5.57664 9.05322C5.57664 9.1517 5.6158 9.24618 5.68606 9.31644L7.5482 11.1786C7.61846 11.2489 7.71294 11.288 7.81142 11.288C7.9099 11.288 8.00438 11.2489 8.07464 11.1786L11.5348 7.69844Z"
                    fill=""
                  />
                </svg>
              </div>
              <div>
                <p className="font-medium text-gray-700 text-theme-sm dark:text-white">
                  Your order is delivered
                </p>
                <p className="mt-1 text-gray-500 text-theme-xs dark:text-gray-400">
                  Amet minim mollit non deserunt ullamco est sit aliqua dolor do
                </p>
                <p className="mt-1 text-gray-400 text-theme-xs dark:text-gray-500">
                  1 hour ago
                </p>
              </div>
            </DropdownItem>
          </li>
        </ul>

        <button className="flex items-center justify-center w-full py-2 mt-1 font-medium text-gray-700 rounded-lg text-theme-sm dark:text-white">
          View all notifications
        </button>
      </Dropdown>
    </div>
  );
};

export default NotificationDropdown;
