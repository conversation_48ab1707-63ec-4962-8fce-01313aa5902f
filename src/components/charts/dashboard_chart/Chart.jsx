import React, { useEffect, useState } from "react";
import axios from "axios";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  Area,
  AreaChart,
  ReferenceLine,
} from "recharts";
import dayjs from "dayjs";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

export default function UserGraph() {
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedPeriod, setSelectedPeriod] = useState("This Week");
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);

  const totalUsers = data.reduce((sum, item) => sum + item.count, 0);
  const avgUsers = data.length > 0 ? Math.round(totalUsers / data.length) : 0;
  const maxUsers =
    data.length > 0 ? Math.max(...data.map((item) => item.count)) : 0;

  const fetchGraphData = async (start, end) => {
    try {
      setIsLoading(true);
      setError(null);

      const token = localStorage.getItem("token");

      const res = await axios.get(
        `https://room8.flexioninfotech.com/api/user-graph/?start_date=${start}&end_date=${end}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (res.data.status) {
        const formattedData = res.data.data.map((item) => ({
          date: dayjs(item.date).format("MMM DD"),
          count: item.count,
        }));
        setData(formattedData);
      } else {
        setError("Failed to fetch data");
      }
    } catch (error) {
      console.error("Error fetching graph data", error);
      setError("Error fetching graph data");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Default to this week on load
    const today = dayjs();
    const thisWeekStart = today.startOf("week");
    fetchGraphData(
      thisWeekStart.format("YYYY-MM-DD"),
      today.format("YYYY-MM-DD")
    );
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isDropdownOpen && !event.target.closest(".dropdown-container")) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isDropdownOpen]);

  const handlePeriodSelect = (period) => {
    setSelectedPeriod(period);
    setIsDropdownOpen(false);

    const today = dayjs();

    if (period === "This Week") {
      const thisWeekStart = today.startOf("week");
      fetchGraphData(
        thisWeekStart.format("YYYY-MM-DD"),
        today.format("YYYY-MM-DD")
      );
      setShowDatePicker(false);
    } else if (period === "This Month") {
      const thisMonthStart = today.startOf("month");
      fetchGraphData(
        thisMonthStart.format("YYYY-MM-DD"),
        today.format("YYYY-MM-DD")
      );
      setShowDatePicker(false);
    } else if (period === "Last Month") {
      const lastMonthStart = today.subtract(1, "month").startOf("month");
      const lastMonthEnd = today.subtract(1, "month").endOf("month");
      fetchGraphData(
        lastMonthStart.format("YYYY-MM-DD"),
        lastMonthEnd.format("YYYY-MM-DD")
      );
      setShowDatePicker(false);
    } else if (period === "Custom Date") {
      setShowDatePicker(true);
    }
  };

  const handleCustomRange = () => {
    if (startDate && endDate) {
      const formattedStartDate = dayjs(startDate).format("YYYY-MM-DD");
      const formattedEndDate = dayjs(endDate).format("YYYY-MM-DD");
      fetchGraphData(formattedStartDate, formattedEndDate);
      setShowDatePicker(false);
    }
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-gradient-to-br from-slate-900 to-slate-800 border border-yellow-400/30 rounded-xl p-4 shadow-2xl backdrop-blur-sm">
          <p className="text-yellow-400 font-semibold text-sm mb-1">{label}</p>
          <p className="text-white font-bold text-lg">
            {payload[0].value} users
          </p>
          <div className="w-full h-px bg-gradient-to-r from-transparent via-yellow-400/50 to-transparent mt-2"></div>
        </div>
      );
    }
    return null;
  };

  const CustomDot = (props) => {
    const { cx, cy, payload } = props;
    return (
      <g>
        {/* Glow effect */}
        <circle
          cx={cx}
          cy={cy}
          r="8"
          fill="rgba(59, 130, 246, 0.2)"
          className="animate-pulse"
        />
        <circle
          cx={cx}
          cy={cy}
          r="4"
          fill="#3b82f6"
          stroke="#facc15"
          strokeWidth="2"
          className="transition-all duration-200 hover:r-6"
        />
      </g>
    );
  };

  if (isLoading) {
    return (
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 to-yellow-50/30 rounded-xl"></div>
        <div className="relative bg-white/80 backdrop-blur-sm rounded-xl p-6 w-full border border-gray-200/50 dark:bg-gray-800/80 dark:border-gray-700/50 shadow-xl">
          <div className="flex items-center justify-center h-96">
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
              <p className="text-gray-600 dark:text-gray-400">
                Loading user data...
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-br from-red-50/30 to-yellow-50/30 rounded-xl"></div>
        <div className="relative bg-white/80 backdrop-blur-sm rounded-xl p-6 w-full border border-gray-200/50 dark:bg-gray-800/80 dark:border-gray-700/50 shadow-xl">
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="text-red-500 text-4xl mb-4">⚠️</div>
              <p className="text-red-600 dark:text-red-400 font-semibold">
                {error}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative ">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 to-yellow-50/30 rounded-xl"></div>
      <div className="absolute top-4 right-4 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-yellow-400/10 rounded-full blur-2xl"></div>
      <div className="absolute bottom-4 left-4 w-24 h-24 bg-gradient-to-br from-yellow-400/10 to-blue-400/10 rounded-full blur-xl"></div>

      <div className="relative  backdrop-blur-sm  pt-3 pe-3 w-full  dark:bg-gray-800/80 dark:border-gray-700/50 border border-gray-200 bg-white rounded-2xl ">
        {/* Header with stats and dropdown */}
        <div className="flex justify-between items-start mb-2">
          <div>
            <h2 className="ms-5 text-2xl font-bold bg-gradient-to-r from-blue-600 to-yellow-500 bg-clip-text text-transparent">
              User Registration Analytics
            </h2>
          </div>

          {/* Dropdown and Stats */}
          <div className="flex items-center gap-4 me-4">
            {/* Time Period Dropdown */}
            {/* Stats cards */}
            {data.length > 0 && (
              <div className="flex gap-4">
                <div className="bg-gradient-to-br from-blue-500 to-blue-600 text-white px-4 py-1 rounded-lg text-center shadow-md">
                  <div className="text-xs opacity-80 px-10">Total</div>
                  <div className="font-bold text-lg">{totalUsers}</div>
                </div>
                <div className="bg-gradient-to-br from-yellow-500 to-yellow-600 text-white px-4 py-1 rounded-lg text-center shadow-md">
                  <div className="text-xs opacity-80 px-10">Average</div>
                  <div className="font-bold text-lg">{avgUsers}</div>
                </div>
                <div className="bg-gradient-to-br from-green-500 to-green-600 text-white px-4 py-1 rounded-lg text-center shadow-md">
                  <div className="text-xs opacity-80 px-10">Peak</div>
                  <div className="font-bold text-lg">{maxUsers}</div>
                </div>
              </div>
            )}

            <div className="relative dropdown-container">
              <button
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                className="flex items-center justify-between px-4 py-3 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-w-[140px]"
              >
                <span className="text-gray-700">{selectedPeriod}</span>
                <svg
                  className={`w-4 h-4 ml-2 transition-transform ${
                    isDropdownOpen ? "rotate-180" : ""
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>

              {/* Dropdown Menu */}
              {isDropdownOpen && (
                <div className="absolute right-0 mt-2 w-[140px] bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                  <div className="py-1">
                    <button
                      onClick={() => handlePeriodSelect("This Week")}
                      className="block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                    >
                      This Week
                    </button>
                    <button
                      onClick={() => handlePeriodSelect("This Month")}
                      className="block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                    >
                      This Month
                    </button>
                    <button
                      onClick={() => handlePeriodSelect("Last Month")}
                      className="block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                    >
                      Last Month
                    </button>
                    <button
                      onClick={() => handlePeriodSelect("Custom Date")}
                      className="block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                    >
                      Custom Date
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Custom Date Picker */}
        {showDatePicker && (
          <div className="absolute top-16 right-0  flex items-center justify-center z-50 ">
            <div className="bg-[#ecf3ff] p-5 rounded-2xl ">
              <div className="inline-calendar-container">
                <DatePicker
                  selected={startDate}
                  onChange={(dates) => {
                    const [start, end] = dates;
                    setStartDate(start);
                    setEndDate(end);
                  }}
                  startDate={startDate}
                  endDate={endDate}
                  selectsRange
                  inline
                  maxDate={new Date()}
                  monthsShown={1}
                  calendarClassName="custom-inline-calendar"
                />
              </div>

              <div className="ms-4 flex gap-2 mt-2">
                <button
                  onClick={handleCustomRange}
                  disabled={!startDate || !endDate}
                  className="px-4 py-1.5 text-sm bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed font-medium"
                >
                  Apply Range
                </button>
                <button
                  onClick={() => {
                    setShowDatePicker(false);
                    setStartDate(null);
                    setEndDate(null);
                  }}
                  className="px-5 py-1.5 text-sm bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors font-medium"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Chart Container */}
        <div className="relative ">
          <ResponsiveContainer width="100%" height={450}>
            <AreaChart
              data={data}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              {/* Gradient Definitions */}
              <defs>
                <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.6} />
                  <stop offset="50%" stopColor="#6366f1" stopOpacity={0.3} />
                  <stop offset="95%" stopColor="#facc15" stopOpacity={0.1} />
                </linearGradient>

                <linearGradient id="lineGradient" x1="0" y1="0" x2="1" y2="0">
                  <stop offset="0%" stopColor="#3b82f6" />
                  <stop offset="50%" stopColor="#6366f1" />
                  <stop offset="100%" stopColor="#facc15" />
                </linearGradient>

                {/* Glow filter */}
                <filter id="glow">
                  <feGaussianBlur stdDeviation="3" result="coloredBlur" />
                  <feMerge>
                    <feMergeNode in="coloredBlur" />
                    <feMergeNode in="SourceGraphic" />
                  </feMerge>
                </filter>
              </defs>

              {/* Enhanced Grid */}
              <CartesianGrid
                strokeDasharray="3 3"
                stroke="#e5e7eb"
                opacity={0.3}
                vertical={false}
              />

              {/* Axes */}
              <XAxis
                dataKey="date"
                stroke="#6b7280"
                tick={{ fontSize: 11, fill: "#6b7280" }}
                tickLine={{ stroke: "#d1d5db" }}
              />
              <YAxis
                stroke="#6b7280"
                tick={{ fontSize: 11, fill: "#6b7280" }}
                tickLine={{ stroke: "#d1d5db" }}
              />

              {/* Average line */}
              {avgUsers > 0 && (
                <ReferenceLine
                  y={avgUsers}
                  stroke="#f59e0b"
                  strokeDasharray="5 5"
                  opacity={0.6}
                  label={{
                    value: "Average",
                    position: "topRight",
                    fill: "#f59e0b",
                  }}
                />
              )}

              {/* Enhanced Tooltip */}
              <Tooltip content={<CustomTooltip />} />

              {/* Area Fill */}
              <Area
                type="monotone"
                dataKey="count"
                stroke="url(#lineGradient)"
                fill="url(#colorGradient)"
                strokeWidth={3}
                dot={<CustomDot />}
                activeDot={{
                  r: 8,
                  fill: "#facc15",
                  stroke: "#3b82f6",
                  strokeWidth: 3,
                  filter: "url(#glow)",
                }}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
}
