import EcommerceMetrics from "../../components/ecommerce/EcommerceMetrics";
import MonthlySalesChart from "../../components/ecommerce/MonthlySalesChart";
import StatisticsChart from "../../components/ecommerce/StatisticsChart";
import RecentOrders from "../../components/ecommerce/RecentOrders";
import UserManagement from "../../components/ecommerce/UserManagement";

import PageMeta from "../../components/common/PageMeta";
import TopSellingProduct from "../../components/ecommerce/TopSellingProduct";
import Chart from "../../components/charts/dashboard_chart/Chart";

export default function Home() {
  return (
    <>
      <PageMeta title="Room 8" description="Room 8" />
      <div className="grid grid-cols-12 gap-4 md:gap-6">
        <div className="col-span-12 ">
          <EcommerceMetrics />
        </div>
        <div className="col-span-12">
          <Chart />
        </div>

        <div className="col-span-12 ">
          <MonthlySalesChart />
        </div>

        <div className="col-span-12">
          <StatisticsChart />
        </div>

        <div className="col-span-12">
          <TopSellingProduct />
        </div>

        <div className="col-span-12">
          <RecentOrders />
        </div>
      </div>
    </>
  );
}
