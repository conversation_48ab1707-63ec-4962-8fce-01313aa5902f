import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import PageMeta from "../../components/common/PageMeta";
import AuthLayout from "../../layout/AuthLayout";
import { useTheme } from "../../context/ThemeContext";
import api from "../../axios/axiosInstance";
import { AlertTriangle } from "lucide-react";

export default function SignUp() {
  const [showPassword, setShowPassword] = useState(false);
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  const { theme } = useTheme();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [isLogin, setIsLogin] = useState(false);
  const [signupSuccess, setSignupSuccess] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const [signupData, setSignupData] = useState({
    first_name: "",
    last_name: "",
    email: "",
    password: "",
  });

  const handleSignupChange = (e) => {
    const { name, value } = e.target;
    setSignupData({
      ...signupData,
      [name]: value,
    });

    // Clear errors when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: undefined,
      });
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // First name validation - at least 3 characters and not just spaces
    if (!signupData.first_name || signupData.first_name.trim().length < 3) {
      newErrors.first_name = "First name must be at least 3 characters";
    }

    // Last name validation - at least 3 characters and not just spaces
    if (!signupData.last_name || signupData.last_name.trim().length < 3) {
      newErrors.last_name = "Last name must be at least 3 characters";
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!signupData.email || !emailRegex.test(signupData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    // Password validation - at least 3 characters and not just spaces
    if (!signupData.password || signupData.password.trim().length < 3) {
      newErrors.password = "Password must be at least 3 characters";
    }

    // Check if terms are agreed
    if (!agreeToTerms) {
      newErrors.terms = "You must agree to the terms and conditions";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    const formData = new FormData();
    formData.append("first_name", signupData.first_name.trim());
    formData.append("last_name", signupData.last_name.trim());
    formData.append("email", signupData.email.trim());
    formData.append("password", signupData.password);

    try {
      // Replace with your actual API endpoint
      const response = await api.post("/api/register", formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });

      setSignupSuccess(true);
      setTimeout(() => setIsLogin(true), 2000);
    } catch (err) {
      console.error("Signup error:", err);

      // Handle API error
      if (err.response?.data?.message) {
        setErrors({
          api: err.response.data.message,
        });
      } else {
        setErrors({
          api: "Registration failed. Please try again.",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <PageMeta
        title="SignUp | Admin Dashboard "
        description="SignUp page Dashboard"
      />
      <AuthLayout>
        <div className="w-full">
          {/* Sign Up Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-semibold text-gray-900 dark:text-white mb-2">
              Sign Up
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Create your account to get started
            </p>
          </div>

          {/* Success Message */}
          {signupSuccess && (
            <div className="mb-4 p-3 bg-green-100 text-green-600 rounded-lg flex items-center">
              <span className="mr-2">✓</span>
              <span>Registration successful! Redirecting to login...</span>
            </div>
          )}

          {/* API Error Message */}
          {errors.api && (
            <div className="mb-4 p-3 bg-red-100 text-red-600 rounded-lg flex items-center">
              <AlertTriangle className="mr-2 h-5 w-5" />
              <span>{errors.api}</span>
            </div>
          )}

          {/* Sign Up Form */}
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
              <div>
                <label
                  htmlFor="firstName"
                  className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  First Name <span className="text-error-500">*</span>
                </label>
                <input
                  name="first_name"
                  type="text"
                  id="first_name"
                  value={signupData.first_name}
                  onChange={handleSignupChange}
                  placeholder="John"
                  className={`w-full px-4 py-2.5 rounded-lg border ${
                    errors.first_name
                      ? "border-red-500"
                      : "border-gray-200 dark:border-gray-700"
                  } bg-white dark:bg-gray-900 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-brand-500 dark:focus:ring-brand-400`}
                />
                {errors.first_name && (
                  <p className="mt-1 flex items-center text-xs text-red-600">
                    <AlertTriangle className="mr-1 h-3 w-3" />{" "}
                    {errors.first_name}
                  </p>
                )}
              </div>
              <div>
                <label
                  htmlFor="lastName"
                  className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  Last Name <span className="text-error-500">*</span>
                </label>
                <input
                  id="last_name"
                  name="last_name"
                  type="text"
                  value={signupData.last_name}
                  onChange={handleSignupChange}
                  placeholder="Doe"
                  className={`w-full px-4 py-2.5 rounded-lg border ${
                    errors.last_name
                      ? "border-red-500"
                      : "border-gray-200 dark:border-gray-700"
                  } bg-white dark:bg-gray-900 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-brand-500 dark:focus:ring-brand-400`}
                />
                {errors.last_name && (
                  <p className="mt-1 flex items-center text-xs text-red-600">
                    <AlertTriangle className="mr-1 h-3 w-3" />{" "}
                    {errors.last_name}
                  </p>
                )}
              </div>
            </div>

            <div className="mb-4">
              <label
                htmlFor="email"
                className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                Email <span className="text-error-500">*</span>
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                value={signupData.email}
                onChange={handleSignupChange}
                placeholder="<EMAIL>"
                className={`w-full px-4 py-2.5 rounded-lg border ${
                  errors.email
                    ? "border-red-500"
                    : "border-gray-200 dark:border-gray-700"
                } bg-white dark:bg-gray-900 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-brand-500 dark:focus:ring-brand-400`}
              />
              {errors.email && (
                <p className="mt-1 flex items-center text-xs text-red-600">
                  <AlertTriangle className="mr-1 h-3 w-3" /> {errors.email}
                </p>
              )}
            </div>

            <div className="mb-6">
              <label
                htmlFor="password"
                className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                Password <span className="text-error-500">*</span>
              </label>
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  name="password"
                  autoComplete="new-password"
                  value={signupData.password}
                  onChange={handleSignupChange}
                  placeholder="Create a password"
                  className={`w-full px-4 py-2.5 rounded-lg border ${
                    errors.password
                      ? "border-red-500"
                      : "border-gray-200 dark:border-gray-700"
                  } bg-white dark:bg-gray-900 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-brand-500 dark:focus:ring-brand-400`}
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400"
                  onClick={togglePasswordVisibility}
                >
                  {showPassword ? (
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M2.5 10C2.5 10 5 5 10 5C15 5 17.5 10 17.5 10C17.5 10 15 15 10 15C5 15 2.5 10 2.5 10Z"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M10 12.5C11.3807 12.5 12.5 11.3807 12.5 10C12.5 8.61929 11.3807 7.5 10 7.5C8.61929 7.5 7.5 8.61929 7.5 10C7.5 11.3807 8.61929 12.5 10 12.5Z"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M4.16699 4.16699L15.8337 15.8337"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  ) : (
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M2.5 10C2.5 10 5 5 10 5C15 5 17.5 10 17.5 10C17.5 10 15 15 10 15C5 15 2.5 10 2.5 10Z"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M10 12.5C11.3807 12.5 12.5 11.3807 12.5 10C12.5 8.61929 11.3807 7.5 10 7.5C8.61929 7.5 7.5 8.61929 7.5 10C7.5 11.3807 8.61929 12.5 10 12.5Z"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 flex items-center text-xs text-red-600">
                  <AlertTriangle className="mr-1 h-3 w-3" /> {errors.password}
                </p>
              )}
              <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                Password must be at least 8 characters long with 1 uppercase, 1
                lowercase, and 1 number
              </p>
            </div>

            <div className="flex items-start mb-6">
              <div className="flex items-center h-5">
                <input
                  type="checkbox"
                  id="terms"
                  checked={agreeToTerms}
                  onChange={() => {
                    setAgreeToTerms(!agreeToTerms);
                    if (errors.terms) {
                      setErrors({
                        ...errors,
                        terms: undefined,
                      });
                    }
                  }}
                  className={`w-4 h-4 text-brand-500 border-gray-300 rounded focus:ring-brand-500 dark:focus:ring-brand-400 dark:border-gray-600 ${
                    errors.terms ? "border-red-500" : ""
                  }`}
                />
              </div>
              <label
                htmlFor="terms"
                className={`ml-2 text-sm ${
                  errors.terms
                    ? "text-red-500"
                    : "text-gray-600 dark:text-gray-400"
                }`}
              >
                I agree to the{" "}
                <Link
                  to=""
                  className="text-brand-500 hover:text-brand-600 dark:text-brand-400 dark:hover:text-brand-300"
                >
                  Terms of Service
                </Link>{" "}
                and{" "}
                <Link
                  to=""
                  className="text-brand-500 hover:text-brand-600 dark:text-brand-400 dark:hover:text-brand-300"
                >
                  Privacy Policy
                </Link>
              </label>
            </div>

            <button
              type="submit"
              className={`w-full ${
                loading
                  ? "bg-brand-400 cursor-not-allowed"
                  : "bg-brand-500 hover:bg-brand-600"
              } text-white py-2.5 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900`}
              disabled={loading}
            >
              {loading ? "Creating Account..." : "Create Account"}
            </button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Already have an account?{" "}
              <Link
                to="/signin"
                className="font-medium text-brand-500 hover:text-brand-600 dark:text-brand-400 dark:hover:text-brand-300"
              >
                Sign In
              </Link>
            </p>
          </div>
        </div>
      </AuthLayout>
    </>
  );
}
