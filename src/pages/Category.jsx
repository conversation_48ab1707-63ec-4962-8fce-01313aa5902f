import React, { useEffect, useState } from "react";
import PageBreadcrumb from "../components/common/PageBreadCrumb";
import PageMeta from "../components/common/PageMeta";
import api from "../axios/axiosInstance";
import { Pencil, Trash2, X, <PERSON><PERSON><PERSON>riangle } from "lucide-react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const Category = () => {
  const [categories, setCategories] = useState([]);
  const [showPopup, setShowPopup] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [form, setForm] = useState({ id: null, name: "" });
  const [name, setName] = useState("");
  const [error, setError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const MAX_CATEGORIES = 12;

  // Get all categories
  const fetchCategories = async () => {
    try {
      const res = await api.get("/api/get-categories");
      setCategories(res.data.categories);
    } catch (error) {
      console.error("Error fetching categories:", error);
    } finally {
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  // Validate form inputs
  const validateForm = () => {
    // Validate category name - at least 3 characters and not just spaces
    if (!name || name.trim().length < 3) {
      setError("Category name must be at least 3 characters");
      return false;
    }

    setError("");
    return true;
  };

  // Handle form submit for Add & Update
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    // Check category limit
    if (!editMode && categories.length >= MAX_CATEGORIES) {
      toast.error(`Maximum ${MAX_CATEGORIES} categories allowed!`);
      return;
    }

    setIsSubmitting(true);

    try {
      if (editMode) {
        const formData = new FormData();
        formData.append("name", name.trim());
        await api.put(`/api/update-category/${form.id}`, formData);
        toast.success("Category updated successfully!");
      } else {
        const formData = new FormData();
        formData.append("name", name.trim());
        await api.post("/api/add-categories", formData);
        toast.success("Category added successfully!");
      }

      setForm({ id: null, name: "" });
      setName("");
      setShowPopup(false);
      setEditMode(false);
      setError("");
      await fetchCategories();
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Something went wrong!");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Delete Category
  const handleDelete = async (id) => {
    try {
      await api.delete(`/api/delete-category/${id}`);
      toast.success("Category deleted successfully!");
      await fetchCategories();
    } catch (error) {
      console.error("Error deleting category:", error);
      toast.error("Failed to delete category!");
    }
  };

  // Open edit popup
  const handleEdit = (category) => {
    setForm(category);
    setName(category.name);
    setEditMode(true);
    setError("");
    setShowPopup(true);
  };

  return (
    <div>
      <PageMeta
        title="Category | Admin Dashboard"
        description="Calendar page Admin Dashboard"
      />
      <PageBreadcrumb pageTitle="Category" />

      <div className="rounded-2xl border border-gray-200 bg-white  dark:border-gray-800 dark:bg-white/[0.03] px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1">
        <div className="flex flex-col gap-y-6">
          <div className="card">
            <div className="flex justify-between">
              <p className="card-title dark:text-white">All Categories</p>
              <div>
                <button
                  className="btn bg-blue-500 text-white flex items-center gap-2 p-2 rounded-md"
                  onClick={() => {
                    setShowPopup(true);
                    setForm({ id: null, name: "" });
                    setName("");
                    setEditMode(false);
                    setError("");
                  }}
                >
                  <span>+ Add Category</span>
                </button>
              </div>
            </div>

            <div className="card-body -mx-5 overflow-auto p-0 mt-4">
              <div className="overflow-x-auto">
                <table className="w-full min-w-[500px]">
                  <thead className="border-y border-gray-100 dark:border-gray-800">
                    <tr>
                      <th className="whitespace-nowrap px-5 py-3 text-left text-xs font-semibold uppercase tracking-wider text-gray-600">
                        ID
                      </th>
                      <th className="whitespace-nowrap px-5 py-3 text-left text-xs font-semibold uppercase tracking-wider text-gray-600">
                        Category Name
                      </th>
                      <th className="whitespace-nowrap px-5 py-3 text-right text-xs font-semibold uppercase tracking-wider text-gray-600">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-100 dark:divide-gray-800">
                    {Array.isArray(categories) && categories.length > 0 ? (
                      categories.map((cat) => (
                        <tr key={cat.id}>
                          <td className="whitespace-nowrap px-5 py-4">
                            <span className="badge badge-primary dark:text-white">
                              {cat.id}
                            </span>
                          </td>
                          <td className="whitespace-nowrap px-5 py-4 font-medium text-gray-800 dark:text-white">
                            {cat.name}
                          </td>
                          <td className="whitespace-nowrap px-5 py-4 text-right">
                            <div className="flex justify-end gap-2">
                              <button
                                className="inline-flex h-8 w-8 items-center justify-center rounded-full bg-purple-100 text-primary transition-all"
                                onClick={() => handleEdit(cat)}
                              >
                                <Pencil size={16} />
                              </button>
                              <button
                                className="inline-flex h-8 w-8 items-center justify-center rounded-full bg-red-100 text-red-600 transition-all"
                                onClick={() => handleDelete(cat.id)}
                              >
                                <Trash2 size={16} />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td
                          colSpan="3"
                          className="px-5 py-8 text-center text-gray-500"
                        >
                          No categories found. Add your first category.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Popup Modal */}
          {showPopup && (
            <div className="fixed inset-0 z-30 flex items-center justify-center bg-white dark:bg-gray-800 bg-opacity-10  backdrop-blur-sm">
              <div className="w-full max-w-md rounded-xl bg-white dark:bg-gray-950 p-6 shadow-2xl">
                <div className="mb-4 flex items-center justify-between">
                  <h3 className="text-xl font-bold text-gray-800 dark:text-white ">
                    {editMode ? "Edit Category" : "Add New Category"}
                  </h3>
                  <button
                    onClick={() => setShowPopup(false)}
                    className="rounded-full p-1 hover:bg-gray-300"
                  >
                    <X size={18} className="text-gray-500" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="relative">
                    <label className="mb-1.5 block text-sm font-medium text-gray-700 dark:text-white">
                      Category Name
                    </label>
                    <input
                      type="text"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      placeholder="Enter category name"
                      className={`w-full rounded-lg border ${
                        error ? "border-red-500" : "border-gray-300"
                      } px-4 py-2.5 text-gray-700`}
                    />
                    {error && (
                      <p className="mt-1 flex items-center text-xs text-red-600">
                        <AlertTriangle className="mr-1 h-3 w-3" /> {error}
                      </p>
                    )}
                  </div>
                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setShowPopup(false)}
                      className="rounded-lg border border-gray-300 dark:border-gray-700 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className={`rounded-lg ${
                        isSubmitting
                          ? "bg-blue-400 cursor-not-allowed"
                          : "bg-blue-600 hover:bg-blue-700"
                      } px-4 py-2 text-sm font-medium text-white`}
                    >
                      {isSubmitting
                        ? editMode
                          ? "Updating..."
                          : "Adding..."
                        : editMode
                        ? "Update Category"
                        : "Add Category"}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Category;
