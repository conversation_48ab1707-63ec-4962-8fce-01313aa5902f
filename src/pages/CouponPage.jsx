import React, { useEffect, useState } from "react";
import PageBreadcrumb from "../components/common/PageBreadCrumb";
import PageMeta from "../components/common/PageMeta";
import api from "../axios/axiosInstance";
import { Pencil, Trash2, <PERSON>, <PERSON><PERSON><PERSON>riangle } from "lucide-react";
import { toast } from "react-toastify";

const CouponPage = () => {
  const [coupon, setCoupon] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [code, setCode] = useState("");
  const [discount_amount, setDiscount_amount] = useState("");
  const [min_order_amount, setMin_order_amount] = useState("");
  const [editingId, setEditingId] = useState(null);
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get all coupons
  const fetchCoupons = async () => {
    try {
      const res = await api.get("/api/get-available-coupons");
      setCoupon(res.data.coupons || []);
      
    } catch (err) {
      console.error("Fetch error:", err);
    }
  };

  useEffect(() => {
    fetchCoupons();
  }, []);

  // Validate form inputs
  const validateForm = () => {
    const newErrors = {};

    // Validate coupon code - at least 3 characters and not just spaces
    if (!code || code.trim().length < 3) {
      newErrors.code = "Coupon code must be at least 3 characters";
    }

    // Validate discount amount - must be a positive number
    if (
      !discount_amount ||
      isNaN(discount_amount) ||
      Number(discount_amount) <= 0
    ) {
      newErrors.discount_amount = "Discount amount must be a positive number";
    }

    // Validate minimum order amount - must be a positive number
    if (
      !min_order_amount ||
      isNaN(min_order_amount) ||
      Number(min_order_amount) <= 0
    ) {
      newErrors.min_order_amount =
        "Minimum order amount must be a positive number";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submit (create or update)
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const formData = new FormData();
      formData.append("code", code.trim());
      formData.append("discount_amount", discount_amount);
      formData.append("min_order_amount", min_order_amount);

      if (editingId) {
        // Update existing coupon
        await api.put(`/api/admin/update-coupon/${editingId}`, formData);
        toast.success("Coupon updated successfully!");
      } else {
        // Create new coupon
        await api.post("/api/admin/create-coupon", formData);
        toast.success("Coupon added successfully!");
      }

      resetForm();
      await fetchCoupons();
    } catch (err) {
      console.error("Submit error:", err);
      toast.error(
        editingId ? "Failed to update coupon!" : "Failed to add coupon!"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setCode("");
    setDiscount_amount("");
    setMin_order_amount("");
    setShowModal(false);
    setEditingId(null);
    setErrors({});
  };

  const handleEdit = (coupon) => {
    setCode(coupon.code);
    setDiscount_amount(coupon.discount_amount);
    setMin_order_amount(coupon.min_order_amount);
    setEditingId(coupon.id);
    setErrors({});
    setShowModal(true);
  };

  // Handle delete
  const handleDelete = async (id) => {
    try {
      await api.delete(`/api/admin/delete-coupon/${id}`);
      toast.success("Coupon deleted successfully!");
      await fetchCoupons();
    } catch (err) {
      console.error("Delete error:", err);
      toast.error("Failed to delete coupon!");
    }
  };

  return (
    <div>
      <PageMeta
        title="Coupon | Admin Dashboard"
        description="Coupon page Admin Dashboard"
      />
      <PageBreadcrumb pageTitle="Coupon" />

      <div className="rounded-2xl border border-gray-200 bg-white dark:border-gray-800 dark:bg-white/[0.03] px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1">
        <div className="flex flex-col gap-y-6">
          <div className="card">
            <div className="flex justify-between">
              <p className="card-title dark:text-white">All Coupon</p>
              <div>
                <button
                  className="btn bg-blue-500 text-white flex items-center gap-2 p-2 rounded-md"
                  onClick={() => {
                    setShowModal(true);
                    setErrors({});
                  }}
                >
                  <span>+ Add Coupon</span>
                </button>
              </div>
            </div>

            <div className="card-body -mx-5 overflow-auto p-0 mt-4">
              <div className="overflow-x-auto">
                <table className="w-full min-w-[500px]">
                  <thead className="border-y border-gray-100 dark:border-gray-800">
                    <tr>
                      <th className="whitespace-nowrap px-5 py-3 text-left text-xs font-semibold uppercase tracking-wider text-gray-600 dark:text-gray-400">
                        ID
                      </th>
                      <th className="whitespace-nowrap px-5 py-3 text-left text-xs font-semibold uppercase tracking-wider text-gray-600 dark:text-gray-400">
                        Coupon Code
                      </th>
                      <th className="whitespace-nowrap px-5 py-3 text-left text-xs font-semibold uppercase tracking-wider text-gray-600 dark:text-gray-400">
                        Discount
                      </th>
                      <th className="whitespace-nowrap px-5 py-3 text-left text-xs font-semibold uppercase tracking-wider text-gray-600 dark:text-gray-400">
                        Min Order Amount
                      </th>
                      <th className="whitespace-nowrap px-5 py-3 text-right text-xs font-semibold uppercase tracking-wider text-gray-600 dark:text-gray-400">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-100 dark:divide-gray-800">
                    {Array.isArray(coupon) && coupon.length > 0 ? (
                      coupon.map((coupon) => (
                        <tr key={coupon.id}>
                          <td className="whitespace-nowrap px-5 py-4">
                            <span className="badge badge-primary dark:text-white">
                              {coupon.id}
                            </span>
                          </td>
                          <td className="whitespace-nowrap px-5 py-4 font-medium text-gray-800 dark:text-white">
                            {coupon.code}
                          </td>
                          <td className="whitespace-nowrap px-5 py-4 font-medium text-gray-800 dark:text-white">
                            ₹{coupon.discount_amount}
                          </td>
                          <td className="whitespace-nowrap px-5 py-4 font-medium text-gray-800 dark:text-white">
                            Min. Order: ₹{coupon.min_order_amount}
                          </td>
                          <td className="whitespace-nowrap px-5 py-4 text-right">
                            <div className="flex justify-end gap-2">
                              <button
                                className="inline-flex h-8 w-8 items-center justify-center rounded-full bg-purple-100 text-primary transition-all"
                                onClick={() => handleEdit(coupon)}
                              >
                                <Pencil size={16} />
                              </button>
                              <button
                                className="inline-flex h-8 w-8 items-center justify-center rounded-full bg-red-100 text-red-600 transition-all"
                                onClick={() => handleDelete(coupon.id)}
                              >
                                <Trash2 size={16} />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td
                          colSpan="5"
                          className="px-5 py-8 text-center text-gray-500 dark:text-gray-400"
                        >
                          No coupons found. Add your first coupon.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Modal */}
          {showModal && (
            <div className="fixed inset-0 z-30 flex items-center justify-center bg-white dark:bg-gray-800 bg-opacity-10 backdrop-blur-sm">
              <div className="w-full max-w-md rounded-xl bg-white dark:bg-gray-950 p-6 shadow-2xl dark:bg-boxdark">
                <div className="mb-4 flex items-center justify-between">
                  <h3 className="text-xl font-bold text-gray-800 dark:text-white">
                    {editingId ? "Edit Coupon" : "Add New Coupon"}
                  </h3>
                  <button
                    onClick={() => setShowModal(false)}
                    className="rounded-full p-1 hover:bg-gray-300 dark:hover:bg-gray-700"
                  >
                    <X size={18} className="text-gray-500 dark:text-gray-400" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="p-6">
                  <div className="mb-4">
                    <label
                      htmlFor="code"
                      className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
                    >
                      Coupon Code
                    </label>
                    <input
                      type="text"
                      id="code"
                      value={code}
                      onChange={(e) => setCode(e.target.value)}
                      className={`w-full rounded-lg border ${
                        errors.code
                          ? "border-red-500"
                          : "border-gray-300 dark:border-gray-700"
                      } px-3 py-2 dark:bg-gray-800 dark:text-white focus:border-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-200`}
                      placeholder="e.g. SUMMER20"
                    />
                    {errors.code && (
                      <p className="mt-1 flex items-center text-xs text-red-600 dark:text-red-400">
                        <AlertTriangle className="mr-1 h-3 w-3" /> {errors.code}
                      </p>
                    )}
                  </div>
                  <div className="mb-4">
                    <label
                      htmlFor="discount_amount"
                      className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
                    >
                      Discount Amount (₹)
                    </label>
                    <input
                      type="number"
                      id="discount_amount"
                      value={discount_amount}
                      onChange={(e) => setDiscount_amount(e.target.value)}
                      className={`w-full rounded-lg border ${
                        errors.discount_amount
                          ? "border-red-500"
                          : "border-gray-300 dark:border-gray-700"
                      } px-3 py-2 dark:bg-gray-800 dark:text-white focus:border-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-200`}
                      placeholder="e.g. 100"
                    />
                    {errors.discount_amount && (
                      <p className="mt-1 flex items-center text-xs text-red-600 dark:text-red-400">
                        <AlertTriangle className="mr-1 h-3 w-3" />{" "}
                        {errors.discount_amount}
                      </p>
                    )}
                  </div>
                  <div className="mb-6">
                    <label
                      htmlFor="min_order_amount"
                      className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
                    >
                      Minimum Order Amount (₹)
                    </label>
                    <input
                      type="number"
                      id="min_order_amount"
                      value={min_order_amount}
                      onChange={(e) => setMin_order_amount(e.target.value)}
                      className={`w-full rounded-lg border ${
                        errors.min_order_amount
                          ? "border-red-500"
                          : "border-gray-300 dark:border-gray-700"
                      } px-3 py-2 dark:bg-gray-800 dark:text-white focus:border-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-200`}
                      placeholder="e.g. 500"
                    />
                    {errors.min_order_amount && (
                      <p className="mt-1 flex items-center text-xs text-red-600 dark:text-red-400">
                        <AlertTriangle className="mr-1 h-3 w-3" />{" "}
                        {errors.min_order_amount}
                      </p>
                    )}
                  </div>
                  <div className="flex justify-end gap-3">
                    <button
                      type="button"
                      className="rounded-lg border border-gray-300 dark:border-gray-700 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      onClick={resetForm}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className={`rounded-lg ${
                        isSubmitting
                          ? "bg-blue-400 cursor-not-allowed"
                          : "bg-blue-600 hover:bg-blue-700"
                      } px-4 py-2 text-sm font-medium text-white`}
                    >
                      {isSubmitting
                        ? editingId
                          ? "Updating..."
                          : "Adding..."
                        : editingId
                        ? "Update Coupon"
                        : "Add Coupon"}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CouponPage;
