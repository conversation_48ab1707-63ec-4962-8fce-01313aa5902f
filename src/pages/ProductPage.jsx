import React, { useState, useEffect, useRef, useCallback } from "react";
import { FaEdit, FaTrash, FaEye } from "react-icons/fa";
import api from "../axios/axiosInstance";
import URL from "../axios/URl";
import { Eye, Pencil, Search, Trash2 } from "lucide-react";
import { Link } from "react-router-dom";
import PageMeta from "../components/common/PageMeta";
import PageBreadcrumb from "../components/common/PageBreadCrumb";
import { toast } from "react-toastify";

function ProductPage() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const [selectedCategory, setSelectedCategory] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [categorydata, setCategory] = useState([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const observer = useRef();

  const lastProductElementRef = useCallback(
    (node) => {
      if (loading) return;
      if (observer.current) observer.current.disconnect();
      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasMore && !loadingMore) {
          setPage((prevPage) => prevPage + 1);
        }
      });
      if (node) observer.current.observe(node);
    },
    [loading, hasMore, loadingMore]
  );

  const fetchProducts = async (pageNum = 1, isNewSearch = false) => {
    try {
      setLoadingMore(true);
      const params = {
        search: searchTerm,
        page: pageNum,
      };

      if (selectedCategory !== "") {
        params.category_id = selectedCategory;
      }

      const response = await api.get("/api/admin/get-all-products/", {
        params,
      });

      if (isNewSearch) {
        setProducts(response.data.results);
      } else {
        setProducts((prevProducts) => [
          ...prevProducts,
          ...response.data.results,
        ]);
      }

      // Check if there are more products to load
      setHasMore(response.data.next !== null);
    } catch (error) {
      console.error("Error fetching products:", error);
      toast.error("Failed to load products");
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  useEffect(() => {
    setPage(1);
    setProducts([]);
    fetchProducts(1, true);
  }, [searchTerm, selectedCategory]);

  useEffect(() => {
    if (page > 1) {
      fetchProducts(page);
    }
  }, [page]);

  const categorygetdata = async () => {
    try {
      const response = await api.get("/api/get-categories");
      setCategory(response.data.categories);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    categorygetdata();
  }, []);

  const handleView = (product) => {
    setSelectedProduct(product);
    setIsViewModalOpen(true);
  };

  const handledalete = async (id) => {
    try {
      await api.delete(`/api/delete-product/${id}`);
      toast.success("Product deleted successfully!");
      fetchProducts();
    } catch (error) {
      console.log(error);
      toast.error("Failed to delete product!");
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen dark:bg-gray-900">
        <p className="text-2xl font-bold text-black dark:text-white">
          Loading...
        </p>
      </div>
    );
  }

  return (
    <div>
      <PageMeta
        title="Products | Admin Dashboard "
        description="Products page Admin Dashboard"
      />
      <PageBreadcrumb pageTitle="Products" />
      <div className="p-4 md:p-6 2xl:p-10 bg-white dark:bg-gray-800 rounded-lg shadow-2xl">
        <div className="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
          <h2 className="text-title-md2 font-bold text-black dark:text-white">
            Products Management
          </h2>
          <div className="relative w-80 md:w-[60%] 2xsm:w-full">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search products..."
              className="w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 text-black dark:text-white"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="flex items-center gap-2">
            <select
              className="rounded-lg border border-gray-300 px-3 py-2 text-gray-700 "
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
            >
              <option
                value=""
                className="text-black dark:text-white bg-white dark:bg-black"
              >
                All Categories
              </option>
              {categorydata.map((category) => (
                <option
                  key={category._id || category.id}
                  value={category._id || category.id}
                  className="text-black dark:text-white bg-white dark:bg-black"
                >
                  {category.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="rounded-sm px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1">
          <div className="max-w-full overflow-x-auto">
            <table className="w-full table-auto">
              <thead className="border-y border-gray-200 dark:border-gray-700">
                <tr className="bg-gray-2 text-left dark:bg-meta-4">
                  <th className="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">
                    Image
                  </th>
                  <th className="min-w-[150px] py-4 px-4 font-medium text-black dark:text-white">
                    Product Name
                  </th>
                  <th className="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">
                    Price
                  </th>
                  <th className="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">
                    Old Price
                  </th>
                  <th className="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">
                    Category
                  </th>
                  <th className="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">
                    Stock
                  </th>
                  <th className="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">
                    Status
                  </th>
                  <th className="py-4 px-4 font-medium text-black dark:text-white">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {products.map((product, index) => (
                  <tr
                    key={product.id}
                    ref={
                      index === products.length - 1
                        ? lastProductElementRef
                        : null
                    }
                  >
                    <td className="border-b border-[#eee] dark:border-gray-700 py-5 px-4 dark:border-strokedark">
                      <div className="h-12 w-12 rounded-md overflow-hidden">
                        <img
                          src={`${URL.PHOTO_URL}${product.image[0]}`}
                          alt={product.product_name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </td>
                    <td className="border-b border-[#eee] dark:border-gray-700 py-5 px-4 dark:border-strokedark">
                      <p className="text-black dark:text-white">
                        {product.product_name}
                      </p>
                    </td>
                    <td className="border-b border-[#eee] dark:border-gray-700 py-5 px-4 dark:border-strokedark">
                      <p className="text-black dark:text-white">
                        ₹{product.product_price}
                      </p>
                    </td>
                    <td className="border-b border-[#eee] dark:border-gray-700 py-5 px-4 dark:border-strokedark">
                      <p className="text-black dark:text-white">
                        ₹{product.product_old_price}
                      </p>
                    </td>
                    <td className="border-b border-[#eee] dark:border-gray-700 py-5 px-4 dark:border-strokedark">
                      <p className="text-black dark:text-white">
                        {product.category_name}
                      </p>
                    </td>
                    <td className="border-b border-[#eee] dark:border-gray-700 py-5 px-4 dark:border-strokedark">
                      <p className="text-black dark:text-white">
                        {product.total_quantity}
                      </p>
                    </td>
                    <td className="border-b border-[#eee] dark:border-gray-700 sm:py-5 px-4 2xsm:py-2 2xsm:px-0.5 dark:border-strokedark">
                      <span
                        className={`inline-flex rounded-full bg-opacity-10 sm:py-1 px-3 2xsm:py-0.5 2xsm:px-2 text-sm font-medium ${
                          product.availability
                            ? "text-success-600 bg-success-100"
                            : "text-red-600 bg-red-100"
                        }`}
                      >
                        {product.availability ? "In Stock" : "Out of Stock"}
                      </span>
                    </td>
                    <td className="border-b border-[#eee] dark:border-gray-700 py-5 px-4 dark:border-strokedark">
                      <div className="flex justify-end gap-2">
                        <button
                          className="inline-flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-primary transition-all"
                          onClick={() => handleView(product)}
                        >
                          <Eye size={16} />
                        </button>
                        <Link
                          to={`/add-product/${product.id}`}
                          className="inline-flex h-8 w-8 items-center justify-center rounded-full bg-purple-100 text-primary transition-all"
                        >
                          <Pencil size={16} />
                        </Link>
                        <button
                          className="inline-flex h-8 w-8 items-center justify-center rounded-full bg-red-100 text-red-600 transition-all"
                          onClick={() => handledalete(product.id)}
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {loadingMore && (
            <div className="flex justify-center items-center py-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          )}
        </div>

        {/* View Product Modal */}
        {isViewModalOpen && selectedProduct && (
          <div className="fixed inset-0 z-30 left-64 flex items-center justify-center bg-white dark:bg-gray-900 bg-opacity-20 shadow-lg">
            <div className="bg-[#f5f5f5] dark:bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-semibold text-black dark:text-white">
                  Product Details
                </h3>
                <button
                  onClick={() => setIsViewModalOpen(false)}
                  className="text-black dark:text-white hover:text-gray-500"
                >
                  ✕
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  {selectedProduct.image &&
                    selectedProduct.image.length > 0 && (
                      <img
                        src={`${URL.PHOTO_URL}${selectedProduct.image[0]}`}
                        alt={selectedProduct.product_name}
                        className="w-96 h-80 rounded-lg mb-4 object-cover"
                      />
                    )}

                  <div className="flex mt-4 space-x-2 overflow-x-auto">
                    {selectedProduct.image &&
                      selectedProduct.image.map((img, index) => (
                        <div
                          key={index}
                          className="w-16 h-16 flex-shrink-0 rounded overflow-hidden"
                        >
                          <img
                            src={`${URL.PHOTO_URL}${img}`}
                            alt={`${selectedProduct.product_name} ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      ))}
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-black dark:text-white mb-2">
                    {selectedProduct.product_name}
                  </h4>

                  <div className="mb-4">
                    <span className="text-xl font-bold text-black dark:text-white">
                      ₹{selectedProduct.product_price}
                    </span>
                    {selectedProduct.product_old_price >
                      selectedProduct.product_price && (
                      <span className="text-base line-through text-body ml-2 dark:text-white">
                        ₹{selectedProduct.product_old_price}
                      </span>
                    )}
                    {selectedProduct.discount_percentage > 0 && (
                      <span className="bg-meta-3 text-green-500 text-xs px-2 py-1 rounded ml-2">
                        {selectedProduct.discount_percentage}% OFF
                      </span>
                    )}
                  </div>

                  <p className="text-sm text-body dark:text-bodydark mb-4 dark:text-white">
                    {selectedProduct.product_details}
                  </p>

                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-body dark:text-bodydark">
                        Category:
                      </span>
                      <span className="text-sm text-black dark:text-white">
                        {selectedProduct.category_name}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-body dark:text-bodydark">
                        Availability:
                      </span>
                      <span
                        className={`text-sm ${
                          selectedProduct.availability
                            ? "text-success-600 dark:text-success-600"
                            : "text-danger-600 dark:text-danger-600"
                        }`}
                      >
                        {selectedProduct.availability
                          ? "In Stock"
                          : "Out of Stock"}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-body dark:text-bodydark">
                        Quantity:
                      </span>
                      <span className="text-sm text-black dark:text-white">
                        {selectedProduct.total_quantity}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-body dark:text-bodydark">
                        Sales Count:
                      </span>
                      <span className="text-sm text-black dark:text-white">
                        {selectedProduct.sales_count}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-body dark:text-bodydark">
                        Rating:
                      </span>
                      <span className="text-sm text-black dark:text-white">
                        {selectedProduct.average_rating} (
                        {selectedProduct.total_reviews} reviews)
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-body dark:text-bodydark">
                        Created At:
                      </span>
                      <span className="text-sm text-black dark:text-white">
                        {new Date(
                          selectedProduct.created_at
                        ).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default ProductPage;
