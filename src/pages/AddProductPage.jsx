import { useState, useEffect } from "react";
import { Upload, X, Save, AlertTriangle } from "lucide-react";
import api from "../axios/axiosInstance";
import { useNavigate, useParams } from "react-router-dom";
import URL from "../axios/URl";
import { toast } from "react-toastify";

// Simple helper function to get image URL
const getImageUrl = (url) => {
  if (!url) return "";
  return url.startsWith("http")
    ? url
    : `${URL.PHOTO_URL}${url.startsWith("/") ? "" : "/"}${url}`;
};

// Extract image URL from various formats
const extractImageUrl = (image) => {
  if (!image) return null;

  // If image is a string, use it directly
  if (typeof image === "string") {
    return image;
  }

  // If image is an object, try to find the URL
  if (typeof image === "object" && image !== null) {
    return image.url || image.image_url || image.path || null;
  }

  return null;
};

export default function AddProductPage() {
  // State to store categories from API
  const [categories, setCategories] = useState([]);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [categoryError, setCategoryError] = useState("");

  const { id } = useParams();
  const isEditMode = Boolean(id && id !== "new");
  const navigate = useNavigate();
  // Loading state for fetching product data in edit mode
  const [loadingProduct, setLoadingProduct] = useState(false);
  const [productError, setProductError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [formData, setFormData] = useState({
    product_name: "",
    product_price: "",
    product_old_price: "",
    category_id: "",
    total_quantity: "",
    availability: true,
    product_details: "",
    new_arrival: false,
    is_trending: false,
    is_sale: false,
    selected_tag: "",
    images: [],
  });

  const [previewImages, setPreviewImages] = useState([]);
  const [existingImages, setExistingImages] = useState([]); // For edit mode existing images
  const [imagesToDelete, setImagesToDelete] = useState([]); // Track images to delete
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState("");

  // Fetch categories from API when component mounts
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoadingCategories(true);
        setCategoryError("");

        const response = await api.get("/api/get-categories");
        if (response.data && response.data.categories) {
          setCategories(response.data.categories);
        } else {
          throw new Error("Invalid categories response");
        }
      } catch (error) {
        console.error("Error fetching categories:", error);
        setCategoryError("Failed to load categories. Please refresh the page.");
      } finally {
        setLoadingCategories(false);
      }
    };

    fetchCategories();
  }, []);

  // Fetch product data if in edit mode
  useEffect(() => {
    if (isEditMode && id) {
      const fetchProductData = async () => {
        try {
          setLoadingProduct(true);
          setProductError("");

          const response = await api.get(`/api/get-product-by-id/`, {
            params: {
              product_id: id,
            },
          });

          const productData = response.data.product || response.data;
         

          if (!productData) {
            throw new Error("Product not found");
          }

          // Determine which tag is selected
          let selected_tag = "";
          if (productData.new_arrival === true) {
            selected_tag = "new_arrival";
          } else if (productData.is_trending === true) {
            selected_tag = "is_trending";
          } else if (productData.is_sale === true) {
            selected_tag = "is_sale";
          }

          const formDataToSet = {
            product_name: productData.product_name || "",
            product_price: productData.product_price?.toString() || "",
            product_old_price: productData.product_old_price?.toString() || "",
            category_id: productData.category_id?.toString() || "",
            total_quantity: productData.total_quantity?.toString() || "",
            availability: productData.availability === false ? false : true,
            product_details: productData.product_details || "",
            new_arrival: productData.new_arrival === true,
            is_trending: productData.is_trending === true,
            is_sale: productData.is_sale === true,
            selected_tag: selected_tag,
            images: [],
          };

          
          setFormData(formDataToSet);

          // Process images from different possible API response structures
          let productImages = [];

          if (productData.images) {
            if (Array.isArray(productData.images)) {
              productImages = productData.images;
            } else if (typeof productData.images === "string") {
              try {
                const parsed = JSON.parse(productData.images);
                if (Array.isArray(parsed)) {
                  productImages = parsed;
                } else {
                  productImages = [productData.images];
                }
              } catch (e) {
                productImages = [productData.images];
              }
            }
          } else if (
            productData.product_images &&
            Array.isArray(productData.product_images)
          ) {
            productImages = productData.product_images;
          } else if (productData.image) {
            productImages = Array.isArray(productData.image)
              ? productData.image
              : [productData.image];
          }

          // Process images to ensure consistent format
          if (productImages.length > 0) {
            const processedImages = productImages
              .map((image) => {
                const url = extractImageUrl(image);
                return url ? { url } : null;
              })
              .filter(Boolean);

            setExistingImages(processedImages);
          }
        } catch (error) {
          console.error("Error fetching product data:", error);
          setProductError("Failed to load product data. Please try again.");
        } finally {
          setLoadingProduct(false);
        }
      };

      fetchProductData();
    }
  }, [isEditMode, id]);

  // Find category name based on selected ID
  const selectedCategory = categories.find(
    (cat) => cat.id?.toString() === formData.category_id?.toString()
  );
  const categoryName = selectedCategory ? selectedCategory.name : "";

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === "checkbox" ? checked : value;

    // Handle radio button selection for tags
    if (name === "selected_tag") {
      setFormData((prev) => ({
        ...prev,
        selected_tag: value,
        new_arrival: value === "new_arrival",
        is_trending: value === "is_trending",
        is_sale: value === "is_sale",
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: newValue,
      }));
    }

    // Mark field as touched
    setTouched((prev) => ({
      ...prev,
      [name]: true,
    }));

    // Clear errors when user starts typing/changing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  // Validate fields
  useEffect(() => {
    const newErrors = {};

    // Required field validations
    if (touched.product_name && !formData.product_name.trim()) {
      newErrors.product_name = "Product name is required";
    }

    if (touched.product_details && !formData.product_details.trim()) {
      newErrors.product_details = "Product details are required";
    }

    if (touched.category_id && !formData.category_id) {
      newErrors.category_id = "Category is required";
    }

    if (
      touched.total_quantity &&
      (!formData.total_quantity || Number(formData.total_quantity) < 0)
    ) {
      newErrors.total_quantity = "Total quantity must be a positive number";
    }

    // Price validation
    if (touched.product_price) {
      if (!formData.product_price) {
        newErrors.product_price = "Price is required";
      } else if (
        isNaN(formData.product_price) ||
        Number(formData.product_price) < 0
      ) {
        newErrors.product_price = "Price must be a positive number";
      }
    }

    // Old price validation (if provided)
    if (touched.product_old_price && formData.product_old_price) {
      if (
        isNaN(formData.product_old_price) ||
        Number(formData.product_old_price) < 0
      ) {
        newErrors.product_old_price = "Old price must be a positive number";
      } else if (
        Number(formData.product_old_price) <= Number(formData.product_price)
      ) {
        newErrors.product_old_price =
          "Old price should be greater than current price";
      }
    }

    // Image validation - For edit mode, existing images count towards requirement
    if (touched.images || isEditMode) {
      const totalImages =
        (existingImages?.length || 0) + (formData.images?.length || 0);
      if (totalImages === 0) {
        newErrors.images = "At least one product image is required";
      }
    }

    setErrors(newErrors);
  }, [formData, touched, existingImages, isEditMode]);

  const handleImageUpload = (e) => {
    const files = Array.from(e.target.files);

    if (files.length > 0) {
      // Calculate remaining slots considering existing images
      const currentImageCount =
        (existingImages?.length || 0) + previewImages.length;
      const remainingSlots = 5 - currentImageCount;

      if (remainingSlots <= 0) {
        alert(
          "Maximum 5 images allowed. Please remove some existing images first."
        );
        e.target.value = "";
        return;
      }

      const filesToAdd = files.slice(0, remainingSlots);

      // Validate file sizes and types
      const validFiles = filesToAdd.filter((file) => {
        if (file.size > 10 * 1024 * 1024) {
          alert(`File ${file.name} is too large. Maximum size is 10MB.`);
          return false;
        }
        if (!file.type.startsWith("image/")) {
          alert(`File ${file.name} is not an image.`);
          return false;
        }
        return true;
      });

      // Create previews for new images
      validFiles.forEach((file) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          setPreviewImages((prev) => [...prev, e.target.result]);
        };
        reader.readAsDataURL(file);
      });

      // Add new images to form data
      setFormData((prev) => ({
        ...prev,
        images: [...prev.images, ...validFiles],
      }));

      // Clear image error if images were added
      if (validFiles.length > 0) {
        setErrors((prev) => ({
          ...prev,
          images: undefined,
        }));
      }

      // Mark images as touched
      setTouched((prev) => ({
        ...prev,
        images: true,
      }));
    }

    // Reset file input
    e.target.value = "";
  };

  const removeImage = (index) => {
    setPreviewImages((prev) => prev.filter((_, i) => i !== index));
    setFormData((prev) => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index),
    }));

    // Check if total images would be zero after removal
    const remainingNewImages = formData.images.length - 1;
    const totalImages = (existingImages?.length || 0) + remainingNewImages;

    if (totalImages === 0) {
      setErrors((prev) => ({
        ...prev,
        images: "At least one product image is required",
      }));
    }
  };

  const removeExistingImage = (index) => {
    const imageToDelete = existingImages[index];
    console.log("Removing existing image:", imageToDelete);
    setImagesToDelete((prev) => [...prev, imageToDelete]);
    setExistingImages((prev) => prev.filter((_, i) => i !== index));

    // Check if total images would be zero after removal
    const remainingExistingImages = existingImages.length - 1;
    const totalImages =
      remainingExistingImages + (formData.images?.length || 0);

    if (totalImages === 0) {
      setErrors((prev) => ({
        ...prev,
        images: "At least one product image is required",
      }));
    }
  };

  const touchAll = () => {
    const allFields = {
      product_name: true,
      product_price: true,
      product_old_price: true,
      category_id: true,
      total_quantity: true,
      product_details: true,
      images: true,
    };
    setTouched((prev) => ({ ...prev, ...allFields }));
  };

  // Check if form is valid
  const isFormValid = () => {
    // Check required fields
    if (
      !formData.product_name.trim() ||
      !formData.product_price ||
      !formData.category_id ||
      !formData.total_quantity ||
      !formData.product_details.trim()
    ) {
      return false;
    }

    // Check images - consider both existing and new images
    const totalImages =
      (existingImages?.length || 0) + (formData.images?.length || 0);
    if (totalImages === 0) {
      return false;
    }

    // Check if there are any errors
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    touchAll();

    // Wait for validation to complete
    await new Promise((resolve) => setTimeout(resolve, 100));

    if (!isFormValid()) {
      // Show specific validation errors
      const validationErrors = {};

      if (!formData.product_name.trim())
        validationErrors.product_name = "Product name is required";
      if (!formData.product_price)
        validationErrors.product_price = "Price is required";
      if (!formData.category_id)
        validationErrors.category_id = "Category is required";
      if (!formData.total_quantity)
        validationErrors.total_quantity = "Total quantity is required";
      if (!formData.product_details.trim())
        validationErrors.product_details = "Product details are required";

      const totalImages =
        (existingImages?.length || 0) + (formData.images?.length || 0);
      if (totalImages === 0)
        validationErrors.images = "At least one product image is required";

      setErrors((prev) => ({ ...prev, ...validationErrors }));

      // Scroll to the first error
      setTimeout(() => {
        const firstErrorElement = document.querySelector(".border-red-500");
        if (firstErrorElement) {
          firstErrorElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      }, 100);
      return;
    }

    setSubmitError("");
    setIsSubmitting(true);

    try {
      // Prepare form data for API submission
      const formDataToSubmit = new FormData();

      // Add text fields - ensure they're not empty strings
      formDataToSubmit.append("product_name", formData.product_name.trim());
      formDataToSubmit.append("product_price", formData.product_price);
      formDataToSubmit.append(
        "product_old_price",
        formData.product_old_price || ""
      );
      formDataToSubmit.append("category_id", formData.category_id);
      formDataToSubmit.append("categoryName", categoryName);
      formDataToSubmit.append("total_quantity", formData.total_quantity);
      formDataToSubmit.append("availability", formData.availability);
      formDataToSubmit.append(
        "product_details",
        formData.product_details.trim()
      );

      // Add boolean fields directly
      formDataToSubmit.append("new_arrival", formData.new_arrival.toString());
      formDataToSubmit.append("is_trending", formData.is_trending.toString());
      formDataToSubmit.append("is_sale", formData.is_sale.toString());

      // For edit mode, send existing images info and images to delete
      if (isEditMode) {
        if (existingImages.length > 0) {
          // Convert existing images to a format the API expects
          const existingImagesData = existingImages
            .map((img) => {
              // Get just the path part without the base URL
              const imgUrl = img.url || "";
              return imgUrl.replace(URL.PHOTO_URL, "").replace(/^\//, "");
            })
            .filter(Boolean);

          formDataToSubmit.append(
            "existingImages",
            JSON.stringify(existingImagesData)
          );
        } else {
          // If all existing images were removed, send an empty array
          formDataToSubmit.append("existingImages", JSON.stringify([]));
        }

        if (imagesToDelete.length > 0) {
          // Convert images to delete to a format the API expects
          const imagesToDeleteData = imagesToDelete
            .map((img) => {
              // Get just the path part without the base URL
              const imgUrl = img.url || "";
              return imgUrl.replace(URL.PHOTO_URL, "").replace(/^\//, "");
            })
            .filter(Boolean);

          formDataToSubmit.append(
            "imagesToDelete",
            JSON.stringify(imagesToDeleteData)
          );
        }
      }

      // Add new image files
      formData.images.forEach((image) => {
        formDataToSubmit.append("images", image);
      });

      // Choose API endpoint based on mode
      const apiEndpoint = isEditMode
        ? `/api/update-products/${id}`
        : "/api/add-products";
      const apiMethod = isEditMode ? "put" : "post";

      const response = await api[apiMethod](apiEndpoint, formDataToSubmit, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      setSubmitSuccess(true);
      toast.success(
        isEditMode
          ? "Product updated successfully!"
          : "Product added successfully!"
      );

      // Reset form after successful submission (only for add mode)
      if (!isEditMode) {
        setFormData({
          product_name: "",
          product_price: "",
          product_old_price: "",
          category_id: "",
          total_quantity: "",
          availability: true,
          product_details: "",
          new_arrival: false,
          is_trending: false,
          is_sale: false,
          selected_tag: "",
          images: [],
        });
        setPreviewImages([]);
        setExistingImages([]);
        setTouched({});
        setErrors({});
      }

      navigate("/product");
    } catch (error) {
      console.error(
        `Error ${isEditMode ? "updating" : "adding"} product:`,
        error
      );

      let errorMessage = `Failed to ${
        isEditMode ? "update" : "add"
      } product. Please try again.`;

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.message) {
        errorMessage = error.message;
      }

      setSubmitError(errorMessage);
      toast.error(errorMessage);

      // Scroll to top to show error
      window.scrollTo({ top: 0, behavior: "smooth" });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading spinner while fetching product data in edit mode
  if (isEditMode && loadingProduct) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="flex items-center space-x-2">
          <span className="text-gray-600 dark:text-gray-300">
            Loading product data...
          </span>
        </div>
      </div>
    );
  }

  // Show error if failed to load product data in edit mode
  if (isEditMode && productError) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <AlertTriangle className="mx-auto mb-4 h-12 w-12 text-red-500" />
          <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-white">
            Error Loading Product
          </h3>
          <p className="text-gray-600 dark:text-gray-300">{productError}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 rounded bg-[#B4945E] px-4 py-2 text-white hover:bg-[#8A6D3B]"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 px-4 py-8 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-4xl">
        <h2 className="mb-6 text-xl font-medium text-[#4A3C2A] dark:text-white">
          {isEditMode ? "Edit Product" : "Add Product"}
        </h2>

        <div className="overflow-hidden rounded-lg bg-white dark:bg-gray-800 shadow-lg dark:bg-boxdark">
          <form onSubmit={handleSubmit}>
            <div className="px-4 py-5 sm:p-6">
              <div className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-6">
                {/* Product Name */}
                <div className="sm:col-span-3">
                  <label
                    htmlFor="product_name"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Product Name <span className="text-error-500">*</span>
                  </label>
                  <div className="mt-1">
                    <input
                      type="text"
                      name="product_name"
                      id="product_name"
                      required
                      value={formData.product_name}
                      onChange={handleChange}
                      className={`block w-full rounded-md border ${
                        errors.product_name
                          ? "border-red-500"
                          : "border-gray-300 dark:border-gray-600"
                      } p-2 shadow-sm focus:border-blue-600 focus:ring-blue-600 dark:bg-gray-800 dark:text-white sm:text-sm`}
                    />
                    {errors.product_name && (
                      <p className="mt-1 flex items-center text-xs text-red-600 dark:text-red-400">
                        <AlertTriangle className="mr-1 h-3 w-3" />{" "}
                        {errors.product_name}
                      </p>
                    )}
                  </div>
                </div>

                {/* Price */}
                <div className="sm:col-span-3">
                  <label
                    htmlFor="product_price"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Price (₹) <span className="text-error-500">*</span>
                  </label>
                  <div className="mt-1">
                    <input
                      type="number"
                      name="product_price"
                      id="product_price"
                      required
                      min="0"
                      step="0.01"
                      value={formData.product_price}
                      onChange={handleChange}
                      className={`block w-full rounded-md border ${
                        errors.product_price
                          ? "border-red-500"
                          : "border-gray-300 dark:border-gray-600"
                      } p-2 shadow-sm focus:border-blue-600 focus:ring-blue-600 dark:bg-gray-800 dark:text-white sm:text-sm`}
                    />
                    {errors.product_price && (
                      <p className="mt-1 flex items-center text-xs text-red-600 dark:text-red-400">
                        <AlertTriangle className="mr-1 h-3 w-3" />{" "}
                        {errors.product_price}
                      </p>
                    )}
                  </div>
                </div>

                {/* Old Price */}
                <div className="sm:col-span-3">
                  <label
                    htmlFor="product_old_price"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Old Price (₹)
                  </label>
                  <div className="mt-1">
                    <input
                      type="number"
                      name="product_old_price"
                      id="product_old_price"
                      min="0"
                      step="0.01"
                      value={formData.product_old_price}
                      onChange={handleChange}
                      className={`block w-full rounded-md border ${
                        errors.product_old_price
                          ? "border-red-500"
                          : "border-gray-300 dark:border-gray-600"
                      } p-2 shadow-sm focus:border-blue-600 focus:ring-blue-600 dark:bg-gray-800 dark:text-white sm:text-sm`}
                    />
                    {errors.product_old_price && (
                      <p className="mt-1 flex items-center text-xs text-red-600 dark:text-red-400">
                        <AlertTriangle className="mr-1 h-3 w-3" />{" "}
                        {errors.product_old_price}
                      </p>
                    )}
                  </div>
                </div>

                {/* Category Dropdown */}
                <div className="sm:col-span-3">
                  <label
                    htmlFor="category_id"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Category <span className="text-error-500">*</span>
                  </label>
                  <div className="mt-1">
                    {loadingCategories ? (
                      <div className="flex items-center">
                        <div className="h-5 w-5 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 dark:border-gray-600 dark:border-t-blue-400"></div>
                        <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
                          Loading categories...
                        </span>
                      </div>
                    ) : categoryError ? (
                      <div className="flex items-center text-red-500 dark:text-red-400">
                        <AlertTriangle className="mr-1 h-4 w-4" />
                        <span className="text-sm">{categoryError}</span>
                      </div>
                    ) : (
                      <select
                        id="category_id"
                        name="category_id"
                        required
                        value={formData.category_id}
                        onChange={handleChange}
                        className={`block w-full rounded-md border ${
                          errors.category_id
                            ? "border-red-500"
                            : "border-gray-300 dark:border-gray-600"
                        } p-2 shadow-sm focus:border-black focus:ring-black dark:bg-gray-800 dark:text-white sm:text-sm`}
                        disabled={categories.length === 0}
                      >
                        <option value="">Select a category</option>
                        {categories.map((category) => (
                          <option key={category.id} value={category.id}>
                            {category.name}
                          </option>
                        ))}
                      </select>
                    )}
                    {errors.category_id && (
                      <p className="mt-1 flex items-center text-xs text-red-600 dark:text-red-400">
                        <AlertTriangle className="mr-1 h-3 w-3" />{" "}
                        {errors.category_id}
                      </p>
                    )}
                  </div>
                </div>

                {/* Total Quantity */}
                <div className="sm:col-span-3">
                  <label
                    htmlFor="total_quantity"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Total Quantity <span className="text-error-500">*</span>
                  </label>
                  <div className="mt-1">
                    <input
                      type="number"
                      name="total_quantity"
                      id="total_quantity"
                      required
                      min="0"
                      value={formData.total_quantity}
                      onChange={handleChange}
                      className={`block w-full rounded-md border ${
                        errors.total_quantity
                          ? "border-red-500"
                          : "border-gray-300 dark:border-gray-600"
                      } p-2 shadow-sm focus:border-blue-600 focus:ring-blue-600 dark:bg-gray-800 dark:text-white sm:text-sm`}
                    />
                    {errors.total_quantity && (
                      <p className="mt-1 flex items-center text-xs text-red-600 dark:text-red-400">
                        <AlertTriangle className="mr-1 h-3 w-3" />{" "}
                        {errors.total_quantity}
                      </p>
                    )}
                  </div>
                </div>

                {/* Availability - Changed from dropdown to checkbox */}
                <div className="sm:col-span-3">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Availability
                  </label>
                  <div className="mt-2 flex items-center">
                    <input
                      type="checkbox"
                      id="availability"
                      name="availability"
                      checked={formData.availability}
                      onChange={handleChange}
                      className="h-4 w-4 rounded border-gray-300 text-[#B4945E] focus:ring-[#B4945E] dark:bg-gray-700 dark:text-white"
                    />
                    <label
                      htmlFor="availability"
                      className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                    >
                      {formData.availability ? "In Stock" : "Out of Stock"}
                    </label>
                  </div>
                </div>

                {/* Product Details */}
                <div className="sm:col-span-6">
                  <label
                    htmlFor="product_details"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Product Details <span className="text-error-500">*</span>
                  </label>
                  <div className="mt-1">
                    <textarea
                      id="product_details"
                      name="product_details"
                      rows={4}
                      required
                      value={formData.product_details}
                      onChange={handleChange}
                      className={`block w-full rounded-md border ${
                        errors.product_details
                          ? "border-red-500"
                          : "border-gray-300 dark:border-gray-600"
                      } p-2 shadow-sm focus:border-[#B4945E] focus:ring-[#B4945E] dark:bg-gray-800 dark:text-white sm:text-sm`}
                    />
                    {errors.product_details && (
                      <p className="mt-1 flex items-center text-xs text-red-600 dark:text-red-400">
                        <AlertTriangle className="mr-1 h-3 w-3" />{" "}
                        {errors.product_details}
                      </p>
                    )}
                  </div>
                </div>

                {/* Product Tags */}
                <div className="sm:col-span-6">
                  <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Product Tags
                  </label>
                  <div className="flex flex-wrap gap-6">
                    {/* New Arrival Toggle */}
                    <div className="flex items-center">
                      <input
                        type="radio"
                        id="new_arrival"
                        name="selected_tag"
                        value="new_arrival"
                        checked={formData.selected_tag === "new_arrival"}
                        onChange={handleChange}
                        className="h-4 w-4 rounded-full border-gray-300 text-[#B4945E] focus:ring-[#B4945E] dark:bg-gray-700 dark:text-white"
                      />
                      <label
                        htmlFor="new_arrival"
                        className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                      >
                        New Arrival
                      </label>
                    </div>

                    {/* Is Trending Toggle */}
                    <div className="flex items-center">
                      <input
                        type="radio"
                        id="is_trending"
                        name="selected_tag"
                        value="is_trending"
                        checked={formData.selected_tag === "is_trending"}
                        onChange={handleChange}
                        className="h-4 w-4 rounded-full border-gray-300 text-[#B4945E] focus:ring-[#B4945E] dark:bg-gray-700 dark:text-white"
                      />
                      <label
                        htmlFor="is_trending"
                        className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                      >
                        Trending
                      </label>
                    </div>

                    {/* Is Sale Toggle */}
                    <div className="flex items-center">
                      <input
                        type="radio"
                        id="is_sale"
                        name="selected_tag"
                        value="is_sale"
                        checked={formData.selected_tag === "is_sale"}
                        onChange={handleChange}
                        className="h-4 w-4 rounded-full border-gray-300 text-[#B4945E] focus:ring-[#B4945E] dark:bg-gray-700 dark:text-white"
                      />
                      <label
                        htmlFor="is_sale"
                        className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                      >
                        On Sale
                      </label>
                    </div>
                  </div>
                </div>

                {/* Image Upload */}
                <div className="sm:col-span-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    {isEditMode ? "Product Images" : "Product Images"} (Up to 5)
                    <span className="text-error-500">*</span>
                  </label>
                  <div
                    className={`mt-1 flex justify-center rounded-md border-2 ${
                      errors.images
                        ? "border-red-500"
                        : "border-dashed border-blue-600 dark:border-blue-400"
                    } px-6 pb-6 pt-5 dark:bg-gray-800/30`}
                  >
                    <div className="space-y-1 text-center">
                      <div className="flex justify-center text-sm text-gray-600 dark:text-gray-300">
                        <label
                          htmlFor="file-upload"
                          className="relative cursor-pointer rounded-md font-medium text-blue-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-600 focus-within:ring-offset-2 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          <span>Upload Images</span>
                          <input
                            id="file-upload"
                            name="images"
                            type="file"
                            className="sr-only"
                            accept="image/*"
                            multiple
                            onChange={handleImageUpload}
                            disabled={
                              (existingImages?.length || 0) +
                                previewImages.length >=
                              5
                            }
                          />
                        </label>
                      </div>
                      <div className="flex justify-center">
                        <Upload className="mx-auto h-12 w-12 text-blue-600 dark:text-blue-400" />
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        PNG, JPG, GIF up to 10MB
                      </p>
                      {errors.images && (
                        <p className="flex items-center justify-center text-xs font-medium text-red-500 dark:text-red-400">
                          <AlertTriangle className="mr-1 h-3 w-3" />{" "}
                          {errors.images}
                        </p>
                      )}

                      {(existingImages?.length || 0) + previewImages.length >=
                        5 && (
                        <p className="text-xs font-medium text-red-500 dark:text-red-400">
                          Maximum 5 images allowed
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Combined Image Display Section */}
                {(existingImages.length > 0 || previewImages.length > 0) && (
                  <div className="sm:col-span-6">
                    <div className="mt-2">
                      <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Selected Images (
                        {(existingImages?.length || 0) + previewImages.length}
                        /5)
                      </label>
                      <div className="grid grid-cols-2 gap-4 2xsm:grid-cols-1 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
                        {/* Existing Images */}
                        {existingImages.map((image, index) => (
                          <div key={`existing-${index}`} className="relative">
                            <img
                              src={`${URL.PHOTO_URL}${image.url}`}
                              alt={`Existing ${index + 1}`}
                              className="h-32 w-full rounded-md border border-gray-300 dark:border-gray-600 object-cover"
                            />
                            <button
                              type="button"
                              onClick={() => removeExistingImage(index)}
                              className="absolute right-1 top-1 rounded-full bg-red-500 p-1 text-white hover:bg-red-600 focus:outline-none"
                            >
                              <X size={16} />
                            </button>
                          </div>
                        ))}

                        {/* New Images */}
                        {previewImages.map((preview, index) => (
                          <div key={`new-${index}`} className="relative">
                            <img
                              src={preview}
                              alt={`Preview ${index + 1}`}
                              className="h-32 w-full rounded-md border border-gray-300 dark:border-gray-600 object-cover"
                            />
                            <button
                              type="button"
                              onClick={() => removeImage(index)}
                              className="absolute right-1 top-1 rounded-full bg-red-500 p-1 text-white hover:bg-red-600 focus:outline-none"
                            >
                              <X size={16} />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Submit Button */}
              <div className="mt-8 grid sm:grid-cols-2 2xsm:grid-cols-1 2xsm:gap-3 2xsm:pb-0 2xsm:pr-0 sm:pb-6 sm:pr-0">
                <button
                  type="button"
                  onClick={() => navigate(-1)}
                  className="2xsm:mr-0 inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={
                    (!isFormValid() && Object.keys(touched).length > 0) ||
                    isSubmitting
                  }
                  className={`inline-flex justify-center rounded-md border border-transparent ${
                    (!isFormValid() && Object.keys(touched).length > 0) ||
                    isSubmitting
                      ? "cursor-not-allowed bg-blue-600 opacity-70"
                      : "bg-blue-600 hover:bg-blue-700"
                  } px-4 py-2 text-sm font-medium text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2`}
                >
                  {isSubmitting ? (
                    <>
                      <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                      {isEditMode ? "Updating..." : "Adding..."}
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      {isEditMode ? "Update Product" : "Add Product"}
                    </>
                  )}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
