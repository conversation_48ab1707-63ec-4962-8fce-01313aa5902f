import React, { useState, useEffect } from "react";
import PageBreadcrumb from "../components/common/PageBreadCrumb";
import PageMeta from "../components/common/PageMeta";
import api from "../axios/axiosInstance";
import URL from "../axios/URl";
import { Eye, Trash2 } from "lucide-react";

const RecentOrders = () => {
  const [orders, setOrders] = useState([]);

  // Fetch orders data
  const fetchOrders = async () => {
    try {
      const res = await api.get("/api/recent-orders");
      setOrders(res.data.recent_orders || []);
      
    } catch (error) {
      console.error("Error fetching orders:", error);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  // Format date to more readable format
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div>
      <PageMeta
        title="Recent Orders |  Admin Dashboard"
        description="Recent Orders page Admin Dashboard"
      />
      <PageBreadcrumb pageTitle="Recent Orders" />

      <div className="rounded-2xl border border-gray-200 bg-white dark:border-gray-800 dark:bg-white/[0.03] px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-black dark:text-white">
            Recent Orders
          </h2>

          <div className="flex items-center gap-2">
            <button className="inline-flex items-center justify-center gap-2.5 rounded-md bg-white dark:bg-gray-800 px-6 py-2 text-sm font-medium text-black hover:bg-opacity-90 border border-stroke dark:border-strokedark dark:bg-boxdark dark:text-white dark:hover:bg-gray-800">
              <svg
                className="w-5 h-5"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
                />
              </svg>
              Filter
            </button>
            <button className="inline-flex items-center justify-center rounded-md bg-white dark:bg-gray-800 px-6 py-2 text-sm font-medium text-black hover:bg-opacity-90 border border-stroke dark:border-strokedark dark:bg-boxdark dark:text-white dark:hover:bg-gray-800">
              See all
            </button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full table-auto">
            <thead className="border-y border-gray-100 dark:border-gray-800">
              <tr>
                <th className="py-4 px-4 font-medium text-black dark:text-white text-left">
                  Order ID
                </th>
                <th className="py-4 px-4 font-medium text-black dark:text-white text-left">
                  Customer
                </th>
                <th className="py-4 px-4 font-medium text-black dark:text-white text-left">
                  Products
                </th>
                <th className="py-4 px-4 font-medium text-black dark:text-white text-left">
                  Total Amount
                </th>
                <th className="py-4 px-4 font-medium text-black dark:text-white text-left">
                  Order Date
                </th>
                <th className="py-4 px-4 font-medium text-black dark:text-white text-left">
                  Action
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-100 dark:divide-gray-800">
              {orders.map((order) => (
                <tr key={order.order_id}>
                  <td className="border-b border-[#eee] dark:border-gray-800  py-5 px-4 dark:border-strokedark">
                    <p className="font-medium text-black dark:text-white">
                      #{order.order_id}
                    </p>
                  </td>
                  <td className="border-b border-[#eee] dark:border-gray-800  py-5 px-4 dark:border-strokedark">
                    <div className="flex items-center gap-3">
                      {order.profile_image && (
                        <div className="h-10 w-10 rounded-full overflow-hidden">
                          <img
                            src={`${URL.PHOTO_URL}${order.profile_image}`}
                            alt={order.name}
                            className="h-full w-full object-cover"
                          />
                        </div>
                      )}
                      <div>
                        <h5 className="font-medium text-black dark:text-white">
                          {order.name}
                        </h5>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {order.email}
                        </p>
                      </div>
                    </div>
                  </td>
                  <td className="border-b border-[#eee] dark:border-gray-800  py-5 px-4 dark:border-strokedark">
                    <div className="flex flex-wrap gap-2">
                      {order.products.map((product, idx) => (
                        <div
                          key={`${order.order_id}-${product.product_id}-${idx}`}
                          className="flex items-center gap-1 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded"
                        >
                          <span className="text-xs dark:text-gray-300">
                            {product.title}
                          </span>
                          <span className="text-xs font-medium text-blue-600 dark:text-blue-400">
                            x {product.quantity}
                          </span>
                        </div>
                      ))}
                    </div>
                  </td>
                  <td className="border-b border-[#eee] dark:border-gray-800  py-5 px-4 dark:border-strokedark">
                    <p className="text-black dark:text-white font-medium">
                      ${order.total_amount.toFixed(2)}
                    </p>
                  </td>
                  <td className="border-b border-[#eee] dark:border-gray-800  py-5 px-4 dark:border-strokedark">
                    <p className="text-black dark:text-white">
                      {formatDate(order.created_at)}
                    </p>
                  </td>
                  <td className="border-b border-[#eee] dark:border-gray-800  py-5 px-4 dark:border-strokedark">
                    <div className="flex justify-start gap-2">
                      <button
                        className="inline-flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-primary transition-all"
                        // onClick={() => handleView(product)}
                      >
                        <Eye size={16} />
                      </button>

                      <button
                        className="inline-flex h-8 w-8 items-center justify-center rounded-full bg-red-100 text-red-600 transition-all"
                        // onClick={() => handledalete(product.id)}
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
              {orders.length === 0 && (
                <tr>
                  <td
                    colSpan="6"
                    className="px-5 py-8 text-center text-gray-500 dark:text-gray-400"
                  >
                    No recent orders found.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default RecentOrders;
