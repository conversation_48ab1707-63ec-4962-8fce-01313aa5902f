import React, { useState, useEffect } from "react";
import {
  <PERSON>aFace<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FaLinkedinIn,
  Fa<PERSON>nstagram,
  FaPencilAlt,
} from "react-icons/fa";
import { useUser } from "../context/UserContext";
import URL from "../axios/URl";
import api from "../axios/axiosInstance";
import { AlertTriangle } from "lucide-react";

const UserProfiles = () => {
  const {
    user,
    address,
    uploadProfileImage,
    updateUser,
    updateAddress,
    refreshUserData,
    fetchUserAddress,
    refreshAllData,
  } = useUser();

  // Separate states for different sections
  const [editingPersonalInfo, setEditingPersonalInfo] = useState(false);
  const [editingAddress, setEditingAddress] = useState(false);
  const [editingProfileHeader, setEditingProfileHeader] = useState(false);

  // Local state for loading and error
  const [localLoading, setLocalLoading] = useState(false);
  const [localError, setLocalError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  const [formErrors, setFormErrors] = useState({
    personalInfo: {},
    address: {},
  });

  // Separate form data for each section
  const [personalInfoData, setPersonalInfoData] = useState({
    first_name: user.first_name || "",
    last_name: user.last_name || "",
    email: user.email || "",
  });

  const [profileHeaderData, setProfileHeaderData] = useState({
    first_name: user.first_name || "",
    last_name: user.last_name || "",
    email: user.email || "",
  });

  const [addressData, setAddressData] = useState({
    full_name: address?.full_name || "",
    id: address?.id || "",
    mobile_number: address?.mobile_number || "",
    house_no: address?.house_no || "",
    area: address?.area || "",
    city: address?.city || "",
    state: address?.state || "",
    country: address?.country || "",
    pincode: address?.pincode || "",
  });

  // Update form data when user or address changes
  useEffect(() => {
    if (user) {
      setPersonalInfoData({
        first_name: user.first_name || "",
        last_name: user.last_name || "",
        email: user.email || "",
      });

      setProfileHeaderData({
        first_name: user.first_name || "",
        last_name: user.last_name || "",
        email: user.email || "",
      });
    }
  }, [user]);

  // Update address data when address changes
  useEffect(() => {
    if (address) {
      setAddressData({
        full_name: address.full_name || "",
        id: address.id || "",
        mobile_number: address.mobile_number || "",
        house_no: address.house_no || "",
        area: address.area || "",
        city: address.city || "",
        state: address.state || "",
        country: address.country || "",
        pincode: address.pincode || "",
      });
    }
  }, [address]);

  // Profile image upload handling
  const [selectedImage, setSelectedImage] = useState(null);

  const handleProfileImageChange = async (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedImage(file);

      try {
        setLocalLoading(true);
        setLocalError(null);

        const result = await uploadProfileImage(file);

        if (result.success) {
          setSuccessMessage("Profile image updated successfully!");
          await refreshAllData();
        } else {
          setLocalError(result.error || "Failed to upload profile image");
        }
      } catch (err) {
        console.error("Error uploading profile image:", err);
        setLocalError("Failed to upload profile image");
      } finally {
        setLocalLoading(false);
      }
    }
  };

  // Form handlers
  const handlePersonalInfoChange = (e) => {
    const { name, value } = e.target;

    setPersonalInfoData({
      ...personalInfoData,
      [name]: value,
    });

    // Clear errors when user types
    if (formErrors.personalInfo[name]) {
      setFormErrors({
        ...formErrors,
        personalInfo: {
          ...formErrors.personalInfo,
          [name]: undefined,
        },
      });
    }
  };

  const handleProfileHeaderChange = (e) => {
    const { name, value } = e.target;

    setProfileHeaderData({
      ...profileHeaderData,
      [name]: value,
    });
  };

  const handleAddressChange = (e) => {
    const { name, value } = e.target;

    setAddressData({
      ...addressData,
      [name]: value,
    });

    // Clear errors when user types
    if (formErrors.address[name]) {
      setFormErrors({
        ...formErrors,
        address: {
          ...formErrors.address,
          [name]: undefined,
        },
      });
    }
  };

  // Validate personal info form
  const validatePersonalInfo = () => {
    const errors = {};

    // First name validation - at least 3 characters and not just spaces
    if (
      !personalInfoData.first_name ||
      personalInfoData.first_name.trim().length < 3
    ) {
      errors.first_name = "First name must be at least 3 characters";
    }

    // Last name validation - at least 3 characters and not just spaces
    if (
      !personalInfoData.last_name ||
      personalInfoData.last_name.trim().length < 3
    ) {
      errors.last_name = "Last name must be at least 3 characters";
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!personalInfoData.email || !emailRegex.test(personalInfoData.email)) {
      errors.email = "Please enter a valid email address";
    }

    setFormErrors({
      ...formErrors,
      personalInfo: errors,
    });

    return Object.keys(errors).length === 0;
  };

  // Validate address form
  const validateAddress = () => {
    const errors = {};

    // Only validate filled fields to ensure they have at least 3 characters when provided
    if (
      addressData.mobile_number &&
      addressData.mobile_number.trim().length < 3
    ) {
      errors.mobile_number = "Phone number must be at least 3 characters";
    }

    if (addressData.house_no && addressData.house_no.trim().length < 3) {
      errors.house_no = "House No must be at least 3 characters";
    }

    if (addressData.area && addressData.area.trim().length < 3) {
      errors.area = "Area must be at least 3 characters";
    }

    if (addressData.city && addressData.city.trim().length < 3) {
      errors.city = "City must be at least 3 characters";
    }

    if (addressData.state && addressData.state.trim().length < 3) {
      errors.state = "State must be at least 3 characters";
    }

    if (addressData.country && addressData.country.trim().length < 3) {
      errors.country = "Country must be at least 3 characters";
    }

    if (addressData.pincode && addressData.pincode.trim().length < 3) {
      errors.pincode = "Postal code must be at least 3 characters";
    }

    setFormErrors({
      ...formErrors,
      address: errors,
    });

    return Object.keys(errors).length === 0;
  };

  const handlePersonalInfoUpdate = async (e) => {
    e.preventDefault();

    // Validate form before submission
    if (!validatePersonalInfo()) {
      return;
    }

    try {
      setLocalLoading(true);
      setLocalError(null);

      // Trim all text fields before submission
      const trimmedData = {
        first_name: personalInfoData.first_name.trim(),
        last_name: personalInfoData.last_name.trim(),
        email: personalInfoData.email.trim(),
      };

      const response = await updateUser(trimmedData);

      if (response && response.success) {
        // Immediately hide the form after successful update
        setEditingPersonalInfo(false);
        setSuccessMessage("Personal information updated successfully!");

        // Refresh all data
        await refreshAllData();
      } else {
        const errorMessage =
          (response && response.error) ||
          "Failed to update personal information. Please try again.";
        console.error("Error details:", errorMessage);
        setLocalError(errorMessage);
      }
    } catch (error) {
      console.error("Exception in personal info update:", error);
      setLocalError(error.message || "An error occurred during update");
    } finally {
      setLocalLoading(false);
    }
  };

  const handleProfileHeaderUpdate = async (e) => {
    e.preventDefault();

    // Immediately hide the form
    setEditingProfileHeader(false);

    try {
      setLocalLoading(true);
      setLocalError(null);

      const response = await updateUser(profileHeaderData);

      if (response && response.success) {
        setSuccessMessage("Profile updated successfully!");

        // Refresh all data
        await refreshAllData();
      } else {
        const errorMessage =
          (response && response.error) ||
          "Failed to update profile. Please try again.";
        console.error("Error details:", errorMessage);
        setLocalError(errorMessage);
      }
    } catch (error) {
      console.error("Exception in profile update:", error);
      setLocalError(error.message || "An error occurred during update");
    } finally {
      setLocalLoading(false);
    }
  };

  const handleAddressSubmit = async (e) => {
    e.preventDefault();

    // Validate form before submission
    if (!validateAddress()) {
      return;
    }

    try {
      setLocalLoading(true);
      setLocalError(null);

      // Trim all text fields before submission
      const trimmedData = {
        ...addressData,
        mobile_number: addressData.mobile_number.trim(),
        house_no: addressData.house_no.trim(),
        area: addressData.area.trim(),
        city: addressData.city.trim(),
        state: addressData.state.trim(),
        country: addressData.country.trim(),
        pincode: addressData.pincode.trim(),
      };

      const response = await updateAddress(trimmedData);

      if (response && response.success) {
        // Immediately hide the form after successful update
        setEditingAddress(false);

        // Show success message
        const actionText = addressData.id ? "updated" : "created";
        setSuccessMessage(`Address ${actionText} successfully!`);

        // Refresh all data to ensure UI displays the latest
        await refreshAllData();
      } else {
        const errorMessage =
          (response && response.error) ||
          "Failed to update address. Please try again.";
        console.error("Error details:", errorMessage);
        setLocalError(errorMessage);
      }
    } catch (error) {
      console.error("Exception in address update:", error);
      setLocalError(error.message || "An error occurred during address update");
    } finally {
      setLocalLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 w-full max-w-7xl">
      <h1 className="text-2xl md:text-3xl font-bold mb-6 dark:text-white text-black">
        Profile
      </h1>

      {/* Profile Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 sm:p-6 mb-6">
        <div className="flex flex-col md:flex-row items-center justify-between md:items-start gap-4">
          <div className="flex flex-col sm:flex-row items-center gap-4 w-full">
            <div className="w-24 h-24 rounded-full overflow-hidden relative">
              <img
                src={
                  user.profile_image
                    ? `${URL.PHOTO_URL}${user.profile_image}`
                    : "https://placehold.co/150x150?text=Profile"
                }
                alt="Profile"
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.target.src = "https://placehold.co/150x150?text=Profile";
                }}
              />
              <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 flex items-center justify-center transition-opacity">
                <label
                  htmlFor="profile-upload"
                  className="cursor-pointer text-white"
                >
                  <FaPencilAlt />
                  <input
                    type="file"
                    id="profile-upload"
                    className="hidden"
                    accept="image/*"
                    onChange={handleProfileImageChange}
                  />
                </label>
              </div>
            </div>

            <div className="text-center sm:text-left">
              <h2 className="text-xl font-bold dark:text-white text-black">
                {user.first_name} {user.last_name}
              </h2>
              <p className="text-gray-600">{user.email}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Personal Information */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 sm:p-6 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-center mb-4 gap-2">
          <h3 className="text-lg font-bold dark:text-white text-black">
            Personal Information
          </h3>
          <button
            className="flex items-center gap-1 px-4 py-2 border rounded-full hover:bg-gray-100 w-full sm:w-auto justify-center sm:justify-start"
            onClick={() => {
              // Reset form data when toggling
              if (!editingPersonalInfo) {
                setPersonalInfoData({
                  first_name: user.first_name || "",
                  last_name: user.last_name || "",
                  email: user.email || "",
                });
                // Clear form errors
                setFormErrors({
                  ...formErrors,
                  personalInfo: {},
                });
              }
              // Clear any errors when toggling
              setLocalError(null);
              setEditingPersonalInfo(!editingPersonalInfo);
            }}
            disabled={localLoading}
          >
            <FaPencilAlt className="text-gray-600" />
            <span>{editingPersonalInfo ? "Cancel" : "Edit"}</span>
          </button>
        </div>

        {editingPersonalInfo ? (
          <form onSubmit={handlePersonalInfoUpdate}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
              <div>
                <label className="text-sm text-gray-500">First Name</label>
                <input
                  type="text"
                  name="first_name"
                  value={personalInfoData.first_name}
                  onChange={handlePersonalInfoChange}
                  className={`w-full border ${
                    formErrors.personalInfo.first_name
                      ? "border-red-500"
                      : "border-gray-300"
                  } rounded-md p-2 mt-1`}
                />
                {formErrors.personalInfo.first_name && (
                  <p className="mt-1 flex items-center text-xs text-red-600">
                    <AlertTriangle className="mr-1 h-3 w-3" />
                    {formErrors.personalInfo.first_name}
                  </p>
                )}
              </div>
              <div>
                <label className="text-sm text-gray-500">Last Name</label>
                <input
                  type="text"
                  name="last_name"
                  value={personalInfoData.last_name}
                  onChange={handlePersonalInfoChange}
                  className={`w-full border ${
                    formErrors.personalInfo.last_name
                      ? "border-red-500"
                      : "border-gray-300"
                  } rounded-md p-2 mt-1`}
                />
                {formErrors.personalInfo.last_name && (
                  <p className="mt-1 flex items-center text-xs text-red-600">
                    <AlertTriangle className="mr-1 h-3 w-3" />
                    {formErrors.personalInfo.last_name}
                  </p>
                )}
              </div>
              <div>
                <label className="text-sm text-gray-500">Email address</label>
                <input
                  type="email"
                  name="email"
                  value={personalInfoData.email}
                  onChange={handlePersonalInfoChange}
                  className={`w-full border ${
                    formErrors.personalInfo.email
                      ? "border-red-500"
                      : "border-gray-300"
                  } rounded-md p-2 mt-1`}
                />
                {formErrors.personalInfo.email && (
                  <p className="mt-1 flex items-center text-xs text-red-600">
                    <AlertTriangle className="mr-1 h-3 w-3" />
                    {formErrors.personalInfo.email}
                  </p>
                )}
              </div>
            </div>
            <div className="mt-4 flex justify-end">
              <button
                type="submit"
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 w-full sm:w-auto"
                disabled={localLoading}
              >
                {localLoading ? "Saving..." : "Save Changes"}
              </button>
            </div>
          </form>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
            <div>
              <p className="text-sm text-gray-500">First Name</p>
              <p className="font-medium dark:text-white">{user.first_name}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Last Name</p>
              <p className="font-medium dark:text-white">{user.last_name}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Email address</p>
              <p className="font-medium dark:text-white">{user.email}</p>
            </div>
          </div>
        )}
      </div>

      {/* Address */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row justify-between items-center mb-4 gap-2">
          <h3 className="text-lg font-bold dark:text-white text-black">
            Details
          </h3>
          <button
            className="flex items-center gap-1 px-4 py-2 border rounded-full hover:bg-gray-100 w-full sm:w-auto justify-center sm:justify-start"
            onClick={() => {
              // Reset address data when toggling
              if (!editingAddress) {
                setAddressData({
                  full_name: address?.full_name || "",
                  id: address?.id || "",
                  mobile_number: address?.mobile_number || "",
                  house_no: address?.house_no || "",
                  area: address?.area || "",
                  city: address?.city || "",
                  state: address?.state || "",
                  country: address?.country || "",
                  pincode: address?.pincode || "",
                });
                // Clear form errors
                setFormErrors({
                  ...formErrors,
                  address: {},
                });
              }
              // Clear any errors when toggling
              setLocalError(null);
              setEditingAddress(!editingAddress);
            }}
            disabled={localLoading}
          >
            <FaPencilAlt className="text-gray-600" />
            <span>{editingAddress ? "Cancel" : "Edit"}</span>
          </button>
        </div>

        {editingAddress ? (
          <form onSubmit={handleAddressSubmit}>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
              {/* Hidden input for address ID */}
              <input type="hidden" name="id" value={addressData.id} />

              <div>
                <label className="text-sm text-gray-500">Phone</label>
                <input
                  type="text"
                  name="mobile_number"
                  value={addressData.mobile_number}
                  onChange={handleAddressChange}
                  className={`w-full border ${
                    formErrors.address.mobile_number
                      ? "border-red-500"
                      : "border-gray-300"
                  } rounded-md p-2 mt-1`}
                />
                {formErrors.address.mobile_number && (
                  <p className="mt-1 flex items-center text-xs text-red-600">
                    <AlertTriangle className="mr-1 h-3 w-3" />
                    {formErrors.address.mobile_number}
                  </p>
                )}
              </div>
              <div>
                <label className="text-sm text-gray-500">House No</label>
                <input
                  type="text"
                  name="house_no"
                  value={addressData.house_no}
                  onChange={handleAddressChange}
                  className={`w-full border ${
                    formErrors.address.house_no
                      ? "border-red-500"
                      : "border-gray-300"
                  } rounded-md p-2 mt-1`}
                />
                {formErrors.address.house_no && (
                  <p className="mt-1 flex items-center text-xs text-red-600">
                    <AlertTriangle className="mr-1 h-3 w-3" />
                    {formErrors.address.house_no}
                  </p>
                )}
              </div>
              <div>
                <label className="text-sm text-gray-500">Area</label>
                <input
                  type="text"
                  name="area"
                  value={addressData.area}
                  onChange={handleAddressChange}
                  className={`w-full border ${
                    formErrors.address.area
                      ? "border-red-500"
                      : "border-gray-300"
                  } rounded-md p-2 mt-1`}
                />
                {formErrors.address.area && (
                  <p className="mt-1 flex items-center text-xs text-red-600">
                    <AlertTriangle className="mr-1 h-3 w-3" />
                    {formErrors.address.area}
                  </p>
                )}
              </div>
              <div>
                <label className="text-sm text-gray-500">City</label>
                <input
                  type="text"
                  name="city"
                  value={addressData.city}
                  onChange={handleAddressChange}
                  className={`w-full border ${
                    formErrors.address.city
                      ? "border-red-500"
                      : "border-gray-300"
                  } rounded-md p-2 mt-1`}
                />
                {formErrors.address.city && (
                  <p className="mt-1 flex items-center text-xs text-red-600">
                    <AlertTriangle className="mr-1 h-3 w-3" />
                    {formErrors.address.city}
                  </p>
                )}
              </div>
              <div>
                <label className="text-sm text-gray-500">State</label>
                <input
                  type="text"
                  name="state"
                  value={addressData.state}
                  onChange={handleAddressChange}
                  className={`w-full border ${
                    formErrors.address.state
                      ? "border-red-500"
                      : "border-gray-300"
                  } rounded-md p-2 mt-1`}
                />
                {formErrors.address.state && (
                  <p className="mt-1 flex items-center text-xs text-red-600">
                    <AlertTriangle className="mr-1 h-3 w-3" />
                    {formErrors.address.state}
                  </p>
                )}
              </div>
              <div>
                <label className="text-sm text-gray-500">Country</label>
                <input
                  type="text"
                  name="country"
                  value={addressData.country}
                  onChange={handleAddressChange}
                  className={`w-full border ${
                    formErrors.address.country
                      ? "border-red-500"
                      : "border-gray-300"
                  } rounded-md p-2 mt-1`}
                />
                {formErrors.address.country && (
                  <p className="mt-1 flex items-center text-xs text-red-600">
                    <AlertTriangle className="mr-1 h-3 w-3" />
                    {formErrors.address.country}
                  </p>
                )}
              </div>
              <div>
                <label className="text-sm text-gray-500">Postal Code</label>
                <input
                  type="text"
                  name="pincode"
                  value={addressData.pincode}
                  onChange={handleAddressChange}
                  className={`w-full border ${
                    formErrors.address.pincode
                      ? "border-red-500"
                      : "border-gray-300"
                  } rounded-md p-2 mt-1`}
                />
                {formErrors.address.pincode && (
                  <p className="mt-1 flex items-center text-xs text-red-600">
                    <AlertTriangle className="mr-1 h-3 w-3" />
                    {formErrors.address.pincode}
                  </p>
                )}
              </div>
            </div>
            <div className="mt-4 flex justify-end">
              <button
                type="submit"
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 w-full sm:w-auto"
                disabled={localLoading}
              >
                {localLoading ? "Saving..." : "Save Changes"}
              </button>
            </div>
          </form>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
            <div>
              <p className="text-sm text-gray-500">Phone</p>
              <p className="font-medium dark:text-white">
                {address?.mobile_number || "Not provided"}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">House No</p>
              <p className="font-medium dark:text-white">
                {address?.house_no || "Not provided"}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Area</p>
              <p className="font-medium dark:text-white">
                {address?.area || "Not provided"}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">City/State</p>
              <p className="font-medium dark:text-white">
                {address?.city || "Not provided"} /{" "}
                {address?.state || "Not provided"}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Country</p>
              <p className="font-medium dark:text-white">
                {address?.country || "Not provided"}
              </p>
            </div>

            <div>
              <p className="text-sm text-gray-500">Postal Code</p>
              <p className="font-medium dark:text-white">
                {address?.pincode || "Not provided"}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Success/Error Messages */}
      {(successMessage || localError) && (
        <div
          className={`mt-4 p-3 rounded-md ${
            successMessage
              ? "bg-green-100 text-green-700"
              : "bg-red-100 text-red-700"
          }`}
        >
          {successMessage || localError}
        </div>
      )}
    </div>
  );
};

export default UserProfiles;
