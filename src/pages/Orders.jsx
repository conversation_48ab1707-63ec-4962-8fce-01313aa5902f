import React, { useEffect, useState } from "react";
import api from "../axios/axiosInstance";
import URL from "../axios/URl";
import { Link } from "react-router-dom";
import PageMeta from "../components/common/PageMeta";
import PageBreadcrumb from "../components/common/PageBreadCrumb";
import { AlertTriangle } from "lucide-react";
import { toast } from "react-toastify";

const statusOptions = [
  "paid",
  "processing",
  "shipped",
  "delivered",
  "cancelled",
];
function Orders() {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [expandedOrder, setExpandedOrder] = useState(null);
  const [statusUpdating, setStatusUpdating] = useState(null);
  const [statusError, setStatusError] = useState({});

  // Fetch orders
  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const response = await api.get("/api/admin-orders-status/");
      if (response.data.status && response.data.orders) {
        setOrders(response.data.orders);
      }
    } catch (error) {
      console.error("Error fetching orders:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (order_id, status) => {
    // Clear error for this order
    setStatusError((prev) => ({
      ...prev,
      [order_id]: undefined,
    }));

    // Validate: ensure status is not empty
    if (!status || status.trim() === "") {
      setStatusError((prev) => ({
        ...prev,
        [order_id]: "Please select a status",
      }));
      return;
    }

    setStatusUpdating(order_id);
    const formdata = new FormData();
    formdata.append("status", status);

    try {
      const response = await api.put(
        `/api/admin-orders-status-update/${order_id}`,
        formdata
      );
      console.log(response);
      fetchOrders();
      toast.success("Order status updated successfully!");
      console.log("Status updated locally and on server");
    } catch (error) {
      console.error(
        "Status update failed",
        error.response?.data || error.message
      );
      toast.error("Failed to update order status!");
      setStatusError((prev) => ({
        ...prev,
        [order_id]: "Failed to update status. Please try again.",
      }));
    } finally {
      setStatusUpdating(null);
    }
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Toggle expanded view for an order
  const toggleExpandOrder = (orderId) => {
    setExpandedOrder(expandedOrder === orderId ? null : orderId);
  };

  return (
    <div className="">
      <PageMeta
        title="Orders | Admin Dashboard "
        description="Orders page Admin Dashboard"
      />
      <PageBreadcrumb pageTitle="Orders" />

      <div className="overflow-hidden rounded-xl p-3 border border-gray-200 bg-white dark:border-gray-800 dark:bg-white/[0.03]">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="border-y border-gray-100 dark:border-gray-800">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400"
                >
                  Order ID
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400"
                >
                  Customer
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400"
                >
                  Amount
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400"
                >
                  Status
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400"
                >
                  Date
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400"
                >
                  Actions
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400"
                >
                  Update Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200 dark:bg-transparent dark:divide-gray-700">
              {loading ? (
                <tr>
                  <td
                    colSpan="7"
                    className="px-6 py-4 text-center text-gray-500 dark:text-gray-400"
                  >
                    Loading orders...
                  </td>
                </tr>
              ) : orders.length === 0 ? (
                <tr>
                  <td
                    colSpan="7"
                    className="px-6 py-4 text-center text-gray-500 dark:text-gray-400"
                  >
                    No orders found
                  </td>
                </tr>
              ) : (
                orders.map((order) => (
                  <React.Fragment key={order.order_id}>
                    <tr className="hover:bg-gray-50 dark:hover:bg-white/[0.02]">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            #{order.order_id}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {order.user.name}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {order.user.email}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          ₹{order.total_amount.toFixed(2)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${
                            order.status === "paid"
                              ? "bg-green-200 text-green-700"
                              : order.status === "processing"
                              ? "bg-blue-200 text-blue-700"
                              : order.status === "shipped"
                              ? "bg-purple-200 text-purple-700"
                              : order.status === "delivered"
                              ? "bg-teal-200 text-teal-700"
                              : order.status === "cancelled"
                              ? "bg-red-200 text-red-700"
                              : "bg-gray-200 text-gray-700"
                          }`}
                        >
                          {order.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {formatDate(order.created_at)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-3">
                          <button
                            onClick={() => toggleExpandOrder(order.order_id)}
                            className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                          >
                            {expandedOrder === order.order_id
                              ? "Hide Details"
                              : "View Details"}
                          </button>
                          <Link
                            to={order.invoice_url}
                            className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                          >
                            Invoice
                          </Link>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col">
                          <select
                            onChange={(e) =>
                              handleStatusChange(order.order_id, e.target.value)
                            }
                            className={`rounded border ${
                              statusError[order.order_id]
                                ? "border-red-500"
                                : "border-gray-300 dark:border-gray-600"
                            } bg-white dark:bg-gray-800 p-1 text-sm text-gray-800 dark:text-white`}
                            defaultValue=""
                          >
                            <option value="">Select status</option>
                            {statusOptions.map((status) => (
                              <option
                                key={status}
                                value={status}
                                selected={order.status === status}
                              >
                                {status}
                              </option>
                            ))}
                          </select>
                          {statusUpdating === order.order_id && (
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              Updating...
                            </p>
                          )}
                          {statusError[order.order_id] && (
                            <p className="text-xs flex items-center text-red-500 mt-1">
                              <AlertTriangle className="h-3 w-3 mr-1" />
                              {statusError[order.order_id]}
                            </p>
                          )}
                        </div>
                      </td>
                    </tr>
                    {expandedOrder === order.order_id && (
                      <tr className="bg-gray-50 dark:bg-gray-800/50">
                        <td colSpan="7" className="px-6 py-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                              <h4 className="font-medium text-gray-800 dark:text-white mb-4 text-lg">
                                Order Items
                              </h4>
                              <div className="space-y-4">
                                {order.items.map((item) => (
                                  <div
                                    key={item.product_id}
                                    className="flex items-center space-x-4 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg"
                                  >
                                    <div className="h-16 w-16 flex-shrink-0">
                                      <img
                                        src={`${URL.PHOTO_URL}${item.image}`}
                                        alt={item.product_name}
                                        className="h-16 w-16 rounded-lg object-cover"
                                      />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                      <p className="text-sm font-medium text-gray-900 truncate dark:text-white">
                                        {item.product_name}
                                      </p>
                                      <p className="text-sm text-gray-500 dark:text-gray-400">
                                        Quantity: {item.quantity}
                                      </p>
                                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                                        ₹{item.price.toFixed(2)}
                                      </p>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                              <h4 className="font-medium text-gray-800 dark:text-white mb-4 text-lg">
                                Shipping Details
                              </h4>
                              {order.address ? (
                                <address className="not-italic text-sm text-gray-500 dark:text-gray-400 space-y-2">
                                  <p className="font-medium text-gray-900 dark:text-white">
                                    {order.address.full_name}
                                  </p>
                                  <p className="font-medium text-gray-900 dark:text-white">
                                    Phone: {order.address.mobile_number}
                                  </p>
                                  <p>{order.address.complete_address}</p>
                                </address>
                              ) : (
                                <p className="text-gray-500 dark:text-gray-400">
                                  No shipping address available
                                </p>
                              )}
                            </div>
                          </div>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default Orders;
