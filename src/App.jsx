import { BrowserRouter, Routes, Route } from "react-router-dom";
import SignIn from "./pages/AuthPages/SignIn";
import SignUp from "./pages/AuthPages/SignUp";
import NotFound from "./pages/OtherPage/NotFound";
import UserProfiles from "./pages/UserProfiles";
import Category from "./pages/Category";
import AppLayout from "./layout/AppLayout";
import Home from "./pages/Dashboard/Home";
import User_Management from "./pages/UserManage/User_Management";
import CouponPage from "./pages/CouponPage";
import ProductPage from "./pages/ProductPage";
import AddProductPage from "./pages/AddProductPage";
import Orders from "./pages/Orders";
import RecentOrders from "./pages/RecentOrders";
import PrivateRoute from "./components/PrivateRoute";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

export default function App() {
  return (
    <>
      <BrowserRouter>
        <Routes>
          <Route element={<AppLayout />}>
            <Route
              index
              path="/"
              element={
                // <PrivateRoute>
                <Home />
                // </PrivateRoute>
              }
            />

            <Route
              path="/profile"
              element={
                // <PrivateRoute>
                <UserProfiles />
                // </PrivateRoute>
              }
            />

            <Route
              path="/User-Management"
              element={
                // <PrivateRoute>
                <User_Management />
                // </PrivateRoute>
              }
            />

            <Route
              path="/category"
              element={
                // <PrivateRoute>
                <Category />
                // </PrivateRoute>
              }
            />
            <Route
              path="/orders"
              element={
                // <PrivateRoute>
                <Orders />
                // </PrivateRoute>
              }
            />
            <Route
              path="/recent-orders"
              element={
                // <PrivateRoute>
                <RecentOrders />
                // </PrivateRoute>
              }
            />
            <Route
              path="/coupon"
              element={
                // <PrivateRoute>
                <CouponPage />
                // </PrivateRoute>
              }
            />
            <Route
              path="/product"
              element={
                // <PrivateRoute>
                <ProductPage />
                // </PrivateRoute>
              }
            />
            <Route
              path="/add-product"
              element={
                // <PrivateRoute>
                <AddProductPage />
                // </PrivateRoute>
              }
            />
            <Route
              path="/add-product/:id"
              element={
                // <PrivateRoute>
                <AddProductPage />
                // </PrivateRoute>
              }
            />
          </Route>

          {/* Auth Layout */}
          <Route path="/signin" element={<SignIn />} />
          <Route path="/signup" element={<SignUp />} />

          {/* Fallback Route */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
        style={{ top: "80px" }}
        toastStyle={{ zIndex: 9999 }}
      />
    </>
  );
}
