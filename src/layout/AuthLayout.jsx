import { Link } from "react-router-dom";
import { useTheme } from "../context/ThemeContext";
import { ThemeToggleButton } from "../components/common/ThemeToggleButton";

const AuthLayout = ({ children }) => {
  const { theme } = useTheme();

  return (
    <div className="flex min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Left side - Form container */}
      <div className="flex flex-col w-full lg:w-1/2 justify-center px-5 sm:px-10 md:px-16 lg:px-20 py-10">
        

        <div className="w-full max-w-md mx-auto">{children}</div>

        
      </div>

      {/* Right side - Brand showcase */}
      <div className="hidden lg:flex lg:w-1/2 bg-brand-600 dark:bg-gray-800 flex-col items-center justify-center relative">
        <div className="absolute top-5 right-5">
          <ThemeToggleButton />
        </div>

        <div className="text-center px-12 max-w-lg">
          <div className="flex justify-center mb-8">
            <div className="inline-flex items-center justify-center text-white bg-white/10 rounded-xl p-4">
              <svg
                width="40"
                height="40"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                  stroke="currentColor"
                  strokeWidth="1.5"
                />
                <path
                  d="M8.5 12H14.5"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                />
                <path
                  d="M12.5 15L15.5 12L12.5 9"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          </div>

          <h1 className="text-3xl md:text-4xl font-semibold text-white mb-4">
            Perfume Dashboard
          </h1>
          

        </div>
      </div>
    </div>
  );
};

export default AuthLayout;
