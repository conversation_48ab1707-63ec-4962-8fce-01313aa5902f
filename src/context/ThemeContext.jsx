import { createContext, useState, useContext, useEffect, useCallback } from "react";

// ThemeContextType would contain:
// {
//   theme: string;
//   toggleTheme: () => void;
// }

const ThemeContext = createContext(undefined);

export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState("light");
  const [isInitialized, setIsInitialized] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    // This code will only run on the client side
    const savedTheme = localStorage.getItem("theme");
    const initialTheme = savedTheme || "light"; // Default to light theme

    if (initialTheme === "dark") {
      document.documentElement.classList.add("dark");
    }
    
    setTheme(initialTheme);
    setIsInitialized(true);
  }, []);

  useEffect(() => {
    if (isInitialized) {
      localStorage.setItem("theme", theme);
    }
  }, [theme, isInitialized]);

  const toggleTheme = useCallback(() => {
    if (isTransitioning) return;
    
    setIsTransitioning(true);
    
    // Add transition class to the root element
    document.documentElement.classList.add("theme-transition");
    
    // Use requestAnimationFrame to ensure smooth transition
    requestAnimationFrame(() => {
      setTheme((prevTheme) => {
        const newTheme = prevTheme === "light" ? "dark" : "light";
        
        if (newTheme === "dark") {
          document.documentElement.classList.add("dark");
        } else {
          document.documentElement.classList.remove("dark");
        }
        
        return newTheme;
      });
      
      // Remove transition class after transition is complete
      setTimeout(() => {
        document.documentElement.classList.remove("theme-transition");
        setIsTransitioning(false);
      }, 300); // Match this with your CSS transition duration
    });
  }, [isTransitioning]);

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
};
