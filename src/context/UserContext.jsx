import { createContext, useState, useContext, useEffect } from "react";
import api from "../axios/axiosInstance";

const UserContext = createContext(undefined);

export const UserProvider = ({ children }) => {
  const [user, setUser] = useState({
    first_name: "",
    last_name: "",
    email: "",
    profile_image: "",
  });

  const [address, setAddress] = useState({
    id: "",
    full_name: "",
    mobile_number: "",
    house_no: "",
    area: "",
    city: "",
    state: "",
    country: "",
    pincode: "",
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const refreshUserData = async () => {
    try {
      setLoading(true);
      const response = await api.get("/api/user-profile");
      if (response.data && response.data.status && response.data.user) {
        setUser(response.data.user);
      }
      setError(null);
      return { success: true };
    } catch (err) {
      console.error("Error fetching user data:", err);
      setError("Failed to load user data");
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  };

  const fetchUserAddress = async () => {
    try {
      setLoading(true);
      const response = await api.get("/api/addresses");
      if (
        response.data &&
        response.data.status &&
        response.data.addresses &&
        response.data.addresses.length > 0
      ) {
        setAddress(response.data.addresses[0]);
      } else {
        console.log("No address found or empty addresses array");
      }
      setError(null);
      return { success: true };
    } catch (err) {
      console.error("Error fetching address data:", err);
      setError("Failed to load address data");
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    refreshUserData();
    fetchUserAddress();
  }, []);

  const updateUser = async (userData) => {
    try {
      setLoading(true);

      // Create FormData and append user data
      const formData = new FormData();
      Object.keys(userData).forEach((key) => {
        formData.append(key, userData[key]);
      });

      // First, update the local state immediately for instant UI feedback
      setUser((prevUser) => ({
        ...prevUser,
        ...userData,
      }));

      const response = await api.put("/api/update-profile", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      if (response.data && response.data.status && response.data.user) {
        // Update with server response data
        setUser(response.data.user);
        // Refresh user data to ensure UI is updated
        await refreshUserData();
        return { success: true, user: response.data.user };
      }
      return {
        success: false,
        error: response.data?.detail || "Update failed",
      };
    } catch (err) {
      console.error("Error updating profile:", err);
      setError("Failed to update profile");
      return {
        success: false,
        error: err.response?.data?.detail || err.message,
      };
    } finally {
      setLoading(false);
    }
  };

  const updateAddress = async (addressData) => {
    try {
      setLoading(true);

      // First, update the local state immediately for instant UI feedback
      setAddress((prevAddress) => ({
        ...prevAddress,
        ...addressData,
      }));

      // Create FormData and append address data
      const formData = new FormData();
      Object.keys(addressData).forEach((key) => {
        formData.append(key, addressData[key]);
      });

      let response;

      // If no address ID, create a new address instead
      if (!addressData.id) {
        // Create a new address
        response = await api.post("/api/create-address/", formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        });
      } else {
        // Update existing address
        const addressId = addressData.id;

        // Include the ID in the URL
        response = await api.put(
          `/api/update-address/${addressId}/`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );
      }

      if (response.data && response.data.status && response.data.address) {
        // Update the address state with server response
        setAddress(response.data.address);

        // Refresh address data to ensure we have the latest
        await fetchUserAddress();
        return { success: true, address: response.data.address };
      }

      return {
        success: false,
        error: response.data?.detail || "Address operation failed",
      };
    } catch (err) {
      console.error("Error with address operation:", err);
      setError("Failed to process address");
      return {
        success: false,
        error: err.response?.data?.detail || err.message,
      };
    } finally {
      setLoading(false);
    }
  };

  const uploadProfileImage = async (imageFile) => {
    try {
      setLoading(true);
      const formData = new FormData();
      formData.append("profile_image", imageFile);

      const response = await api.put("/api/update-profile", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      if (response.data && response.data.status && response.data.user) {
        setUser(response.data.user);
        return { success: true, user: response.data.user };
      }
      setError(null);
      return { success: true };
    } catch (err) {
      console.error("Error uploading profile image:", err);
      setError("Failed to upload profile image");
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  };

  // Add a function to refresh all data at once
  const refreshAllData = async () => {
    try {
      setLoading(true);
      // Refresh user profile
      await refreshUserData();
      // Refresh address data
      await fetchUserAddress();

      return { success: true };
    } catch (err) {
      console.error("Error refreshing data:", err);
      setError("Failed to refresh data");
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  };

  return (
    <UserContext.Provider
      value={{
        user,
        address,
        uploadProfileImage,
        updateUser,
        updateAddress,
        refreshUserData,
        fetchUserAddress,
        refreshAllData,
        loading,
        error,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};
