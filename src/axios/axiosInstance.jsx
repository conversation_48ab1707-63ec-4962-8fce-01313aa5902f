import axios from "axios";

const api = axios.create({
  baseURL: "https://room8.flexioninfotech.com/api/",
});

api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response) {
      const { status } = error.response;

      if (status >= 500 && status <= 599) {
        console.error(`Server Error ${status}: ${error.response.statusText}`);
        window.location.href = "/bad-gateway";
      } else if (status === 403) {
        console.error("Forbidden: Unauthorized access");
        window.location.href = "/PageNotFound";
      } else if (status === 400) {
        console.error(
          "Bad Request:",
          error.response.data?.message || "Invalid Request"
        );
      }
    }

    return Promise.reject(error.response?.data ? error.response.data : error);
  }
);

export default api;
