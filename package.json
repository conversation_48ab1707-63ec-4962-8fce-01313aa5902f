{"name": "tailadmin-react", "private": true, "version": "2.0.2", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@heroicons/react": "^2.2.0", "@react-jvectormap/core": "^1.0.4", "@react-jvectormap/world": "^1.1.2", "apexcharts": "^4.1.0", "axios": "^1.11.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "flatpickr": "^4.6.13", "formik": "^2.4.6", "lucide-react": "^0.513.0", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-datepicker": "^8.4.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-helmet-async": "^2.0.5", "react-icon": "^1.0.0", "react-icons": "^5.5.0", "react-router": "^7.1.5", "react-router-dom": "^7.6.2", "react-toastify": "^11.0.5", "recharts": "^3.1.2", "swiper": "^11.2.3", "tailadmin-react": "file:", "tailwind-merge": "^3.0.1", "yup": "^1.7.0"}, "devDependencies": {"@eslint/js": "^9.19.0", "@tailwindcss/postcss": "^4.0.8", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.5.2", "tailwindcss": "^4.0.8", "vite": "^6.1.0", "vite-plugin-svgr": "^4.3.0"}, "overrides": {"react-helmet-async": {"react": "^16.8.0 || ^17 || ^18 || ^19"}, "@react-jvectormap/core": {"react": "^16.8.0 || ^17 || ^18 || ^19", "react-dom": "^16.8.0 || ^17 || ^18 || ^19"}, "@react-jvectormap/world": {"react": "^16.8.0 || ^17 || ^18 || ^19", "react-dom": "^16.8.0 || ^17 || ^18 || ^19"}}}